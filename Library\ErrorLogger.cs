using System;
using System.IO;
using System.Threading.Tasks;

namespace SamsungTool.Library
{
    /// <summary>
    /// Simple error logger for internal debugging - does not affect user experience
    /// </summary>
    public static class ErrorLogger
    {
        private static readonly string _logPath;
        private static readonly object _lockObject = new object();

        static ErrorLogger()
        {
            var exeDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var logsDirectory = Path.Combine(exeDirectory, "Logs");
            Directory.CreateDirectory(logsDirectory);
            _logPath = Path.Combine(logsDirectory, "error_debug.log");
        }

        /// <summary>
        /// Log error for debugging purposes - silent operation, never throws
        /// </summary>
        public static void LogError(string context, Exception ex)
        {
            try
            {
                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {context}: {ex.GetType().Name} - {ex.Message}";
                if (ex.InnerException != null)
                {
                    logEntry += $" | Inner: {ex.InnerException.Message}";
                }
                logEntry += Environment.NewLine;

                lock (_lockObject)
                {
                    File.AppendAllText(_logPath, logEntry);
                    
                    // Keep log file size manageable (max 1MB)
                    var fileInfo = new FileInfo(_logPath);
                    if (fileInfo.Length > 1024 * 1024)
                    {
                        RotateLogFile();
                    }
                }
            }
            catch
            {
                // Never throw from error logger
            }
        }

        /// <summary>
        /// Log error for debugging purposes - silent operation, never throws
        /// </summary>
        public static void LogError(string context, string message)
        {
            try
            {
                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {context}: {message}{Environment.NewLine}";

                lock (_lockObject)
                {
                    File.AppendAllText(_logPath, logEntry);
                    
                    // Keep log file size manageable
                    var fileInfo = new FileInfo(_logPath);
                    if (fileInfo.Length > 1024 * 1024)
                    {
                        RotateLogFile();
                    }
                }
            }
            catch
            {
                // Never throw from error logger
            }
        }

        private static void RotateLogFile()
        {
            try
            {
                var backupPath = _logPath.Replace(".log", "_backup.log");
                if (File.Exists(backupPath))
                {
                    File.Delete(backupPath);
                }
                File.Move(_logPath, backupPath);
            }
            catch
            {
                // If rotation fails, just delete the current log
                try
                {
                    File.Delete(_logPath);
                }
                catch
                {
                    // Give up silently
                }
            }
        }
    }
}
