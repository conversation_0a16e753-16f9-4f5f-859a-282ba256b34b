<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="SamsungTool.InputSerial"
        Title="Enter New Serial Number"
        Width="380" Height="240"
        WindowStartupLocation="CenterOwner"
        Background="#1E1E1E"
        CanResize="False"
        SystemDecorations="Full">

	<Window.Styles>
		<Style Selector="TextBox#InputBox">
			<Setter Property="Background" Value="#1A1A1A"/>
			<Setter Property="Foreground" Value="#FFFFFF"/>
			<Setter Property="BorderBrush" Value="#3F3F3F"/>
			<Setter Property="SelectionBrush" Value="#A83232"/>
			<Setter Property="SelectionForegroundBrush" Value="#FFFFFF"/>
		</Style>
		<Style Selector="TextBox#InputBox:focus">
			<Setter Property="Background" Value="#1A1A1A"/>
			<Setter Property="BorderBrush" Value="#A83232"/>
		</Style>
		<Style Selector="TextBox#InputBox:pointerover">
			<Setter Property="Background" Value="#1A1A1A"/>
		</Style>
		<Style Selector="TextBox#InputBox /template/ Border">
			<Setter Property="Background" Value="#1A1A1A"/>
		</Style>
		<Style Selector="TextBox#InputBox:focus /template/ Border">
			<Setter Property="Background" Value="#1A1A1A"/>
		</Style>
		<Style Selector="TextBox#InputBox /template/ ScrollViewer">
			<Setter Property="Background" Value="#1A1A1A"/>
		</Style>
		<Style Selector="TextBox#InputBox /template/ TextPresenter">
			<Setter Property="Background" Value="#1A1A1A"/>
		</Style>
	</Window.Styles>

	<Grid RowDefinitions="Auto,*,Auto">
		<Border Grid.Row="0"
                Background="#252525"
                BorderBrush="#3F3F3F"
                BorderThickness="0,0,0,1"
                Padding="16,12">
			<TextBlock x:Name="HeaderTitle"
                       Text="Enter New Serial Number"
                       FontWeight="SemiBold"
                       FontSize="15"
                       Foreground="#F0F0F0"/>
		</Border>

		<StackPanel Grid.Row="1" Margin="16" Spacing="12">
			<TextBlock x:Name="MessageText"
                       Text="Please enter the new serial number for the device:"
                       TextWrapping="Wrap"
                       FontSize="13"
                       Foreground="#E8E8E8"/>

			<TextBox x:Name="InputBox"
                     MaxLength="15"
                     Height="30"
                     Background="#1A1A1A"
                     Foreground="#FFFFFF"
                     BorderBrush="#3F3F3F"
                     BorderThickness="1"
                     CornerRadius="3"
                     Padding="8,5"
                     FontSize="13"
                     VerticalContentAlignment="Center"/>

			<TextBlock x:Name="DescriptionText"
                       Text="The new serial number will replace the current one on the device."
                       TextWrapping="Wrap"
                       Foreground="#A0A0A0"
                       FontSize="11"
                       FontStyle="Italic"
                       Margin="0,-4,0,0"/>
		</StackPanel>

		<Border Grid.Row="2"
                Background="#1E1E1E"
                BorderBrush="#3F3F3F"
                BorderThickness="0,1,0,0"
                Padding="14,10">
			<StackPanel Orientation="Horizontal"
                        Spacing="10"
                        HorizontalAlignment="Right">
				<Button x:Name="CancelButton"
                        Content="Cancel"
                        Width="100"
                        Height="32"
                        Background="#333333"
                        Foreground="#E8E8E8"
                        BorderBrush="#505050"
                        BorderThickness="1"
                        CornerRadius="3"
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"
                        Cursor="Hand"/>
				<Button x:Name="OkButton"
                        Content="OK"
                        Width="100"
                        Height="32"
                        Background="#A83232"
                        Foreground="#FFFFFF"
                        BorderBrush="#A83232"
                        BorderThickness="1"
                        CornerRadius="3"
                        FontWeight="Medium"
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"
                        IsDefault="True"
                        Cursor="Hand"/>
			</StackPanel>
		</Border>
	</Grid>
</Window>