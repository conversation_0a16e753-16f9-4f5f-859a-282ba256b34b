﻿using System.Threading;
using System.Threading.Tasks;
using SamsungTool.Library;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbReadInfoService : AdbServiceBase
    {
        public async Task Run(CancellationToken cancellationToken = default)
        {
            // Use the new common operations utility for standard initialization
            bool initialized = await AdbCommonOperations.ExecuteStandardInitializationAsync(
                InitializeAsync,
                FindDeviceAsync,
                ReadDeviceInfoAsync,
                cancellationToken);

            if (!initialized)
            {
                return;
            }

            // Use the new logging utility for operation completion
            AdbCommonOperations.LogOperationCompleted();
        }
    }
}