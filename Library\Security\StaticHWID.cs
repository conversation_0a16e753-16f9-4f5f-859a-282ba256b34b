﻿namespace SamsungTool.Library.Security
{
    public static class StaticHWID
    {
        private static string? _cachedHWID;
        private static readonly object _lock = new object();

        public static string CurrentHWID
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrEmpty(_cachedHWID))
                    {
                        _cachedHWID = HWIDGenerator.GenerateHWIDAsync().GetAwaiter().GetResult();
                    }
                    return _cachedHWID!;
                }
            }
        }

        public static void ClearCache()
        {
            lock (_lock)
            {
                _cachedHWID = null;
            }
        }

        public static bool IsValidHWID(string hwid)
        {
            return !string.IsNullOrEmpty(hwid) && hwid.Length == 64;
        }
    }
}