using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Avalonia.Media;

namespace SamsungTool.Models
{
    /// <summary>
    /// ViewModel wrapper for LogEntry to support data binding
    /// </summary>
    public class LogEntryViewModel : INotifyPropertyChanged
    {
        private readonly LogEntry _logEntry;

        public LogEntryViewModel(LogEntry logEntry)
        {
            _logEntry = logEntry ?? throw new ArgumentNullException(nameof(logEntry));
        }

        public LogEntry LogEntry => _logEntry;

        public string Id => _logEntry.Id;
        public DateTime StartTime => _logEntry.StartTime;
        public DateTime? EndTime => _logEntry.EndTime;
        public string OperationName => _logEntry.OperationName;
        public string OperationType => _logEntry.OperationType;
        public string LogContent => _logEntry.LogContent;
        public string PlainTextContent => _logEntry.PlainTextContent;
        public LogEntryStatus Status => _logEntry.Status;
        public string FilePath => _logEntry.FilePath;

        public TimeSpan? Duration => _logEntry.Duration;

        public string DurationString
        {
            get
            {
                if (!Duration.HasValue) return "In Progress";
                var duration = Duration.Value;
                if (duration.TotalMinutes >= 1)
                    return $"{duration.Minutes}m {duration.Seconds}s";
                return $"{duration.Seconds}s";
            }
        }

        public string StartTimeString => _logEntry.StartTime.ToString("yyyy-MM-dd HH:mm:ss");

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    LogEntryStatus.Success => "✓ Completed",
                    LogEntryStatus.Failed => "✗ Failed",
                    LogEntryStatus.Cancelled => "⊘ Cancelled",
                    LogEntryStatus.InProgress => "⟳ In Progress",
                    _ => "Unknown"
                };
            }
        }

        public System.Drawing.Color StatusColor
        {
            get
            {
                return Status switch
                {
                    LogEntryStatus.Success => System.Drawing.Color.LimeGreen,
                    LogEntryStatus.Failed => System.Drawing.Color.IndianRed,
                    LogEntryStatus.Cancelled => System.Drawing.Color.Orange,
                    LogEntryStatus.InProgress => System.Drawing.Color.CornflowerBlue,
                    _ => System.Drawing.Color.Silver
                };
            }
        }

        public IBrush StatusBrush
        {
            get
            {
                return Status switch
                {
                    LogEntryStatus.Success => new SolidColorBrush(Color.FromRgb(50, 205, 50)),    // LimeGreen
                    LogEntryStatus.Failed => new SolidColorBrush(Color.FromRgb(205, 92, 92)),     // IndianRed
                    LogEntryStatus.Cancelled => new SolidColorBrush(Color.FromRgb(255, 165, 0)),  // Orange
                    LogEntryStatus.InProgress => new SolidColorBrush(Color.FromRgb(100, 149, 237)), // CornflowerBlue
                    _ => new SolidColorBrush(Color.FromRgb(192, 192, 192))                        // Silver
                };
            }
        }

        public string Summary
        {
            get
            {
                var summary = $"{StartTimeString} - {OperationName}";
                if (Duration.HasValue)
                    summary += $" ({DurationString})";
                return summary;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void RefreshProperties()
        {
            OnPropertyChanged(nameof(EndTime));
            OnPropertyChanged(nameof(Status));
            OnPropertyChanged(nameof(Duration));
            OnPropertyChanged(nameof(DurationString));
            OnPropertyChanged(nameof(StatusDisplay));
            OnPropertyChanged(nameof(StatusColor));
            OnPropertyChanged(nameof(Summary));
        }
    }
}
