using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Library.Security;

/// <summary>
/// Watches for external terminal processes and terminates them if they were not spawned by this app.
/// Designed for Windows (PowerShell, cmd, Windows Terminal) and common shells.
/// </summary>
public static class TerminalBlocker
{
    private static readonly string[] TerminalProcessNames = new[]
    {
        // Windows terminals
        "powershell", "powershell_ise", "pwsh", "cmd", "conhost", "wt", "WindowsTerminal",
        // Common shells (in case user has git-bash or WSL terminals)
        "bash", "zsh", "sh", "fish", "wsl"
    };

    private static Timer? _scanTimer;
    private static readonly TimeSpan _scanInterval = TimeSpan.FromSeconds(3);
    private static volatile bool _running;

    /// <summary>
    /// Starts periodic scanning and immediate blocking of non-tool terminals.
    /// Safe to call multiple times.
    /// </summary>
    public static void Start()
    {
        if (_running) return;
        if (!OperatingSystem.IsWindows())
        {
            // Only enforce on Windows for now
            return;
        }
        _running = true;
        _scanTimer = new Timer(_ => ScanAndBlock(), null, TimeSpan.Zero, _scanInterval);
    }

    /// <summary>
    /// Stops scanning.
    /// </summary>
    public static void Stop()
    {
        _running = false;
        try { _scanTimer?.Dispose(); } catch { }
        _scanTimer = null;
    }

    private static void ScanAndBlock()
    {
        try
        {
            foreach (var name in TerminalProcessNames)
            {
                Process[] procs;
                try
                {
                    procs = Process.GetProcessesByName(name);
                }
                catch
                {
                    continue;
                }

                foreach (var p in procs)
                {
                    try
                    {
                        // Skip if this PID is one of our interactive terminals
                        if (ProcessManager.IsInteractiveTerminalPid(p.Id))
                            continue;

                        // Optional heuristic: allow if started by our current process (parent check is non-trivial on Windows without P/Invoke)
                        // For safety, rely on explicit registration above.

                        TryKillProcess(p);
                    }
                    catch
                    {
                        // ignore per-process errors
                    }
                }
            }
        }
        catch (Exception ex)
        {
            ErrorLogger.LogError("TerminalBlocker.ScanAndBlock", ex);
        }
    }

    private static void TryKillProcess(Process p)
    {
        try
        {
            if (p.HasExited) return;
            p.Kill(entireProcessTree: true);
        }
        catch
        {
        }
    }
}


