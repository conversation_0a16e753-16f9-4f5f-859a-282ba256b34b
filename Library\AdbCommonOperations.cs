using System;
using System.Drawing;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Library
{
    /// <summary>
    /// Common ADB operations utility to reduce code duplication across ADB services
    /// Maintains AOT compatibility and preserves existing error handling patterns
    /// </summary>
    public static class AdbCommonOperations
    {
        /// <summary>
        /// Executes the standard ADB service initialization sequence
        /// </summary>
        /// <param name="initializeFunc">Function to initialize ADB</param>
        /// <param name="findDeviceFunc">Function to find device</param>
        /// <param name="readDeviceInfoFunc">Function to read device info (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if all steps completed successfully, false otherwise</returns>
        public static async Task<bool> ExecuteStandardInitializationAsync(
            Func<CancellationToken, Task<bool>> initializeFunc,
            Func<CancellationToken, Task<bool>> findDeviceFunc,
            Func<CancellationToken, Task<bool>>? readDeviceInfoFunc = null,
            CancellationToken cancellationToken = default)
        {
            if (!await initializeFunc(cancellationToken))
            {
                return false;
            }

            if (!await findDeviceFunc(cancellationToken))
            {
                return false;
            }

            if (readDeviceInfoFunc != null)
            {
                if (!await readDeviceInfoFunc(cancellationToken))
                {
                    return false;
                }
            }

            // Standard delay after initialization
            await Task.Delay(100, cancellationToken);
            return true;
        }

        /// <summary>
        /// Logs operation completion with standard formatting
        /// </summary>
        public static void LogOperationCompleted()
        {
            RichLogs("", Color.Silver, true);
            RichLogs("Operation Completed", Color.LimeGreen, true);
        }

        /// <summary>
        /// Logs operation start with standard formatting
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        public static void LogOperationStart(string operationName)
        {
            RichLogs($"Operation: {operationName}", Color.Silver, true);
        }

        /// <summary>
        /// Logs a step in progress with standard formatting
        /// </summary>
        /// <param name="message">Step message</param>
        /// <param name="isInProgress">True if step is in progress, false if completed</param>
        public static void LogStep(string message, bool isInProgress = true)
        {
            if (isInProgress)
            {
                RichLogs(message, Color.Silver, false);
            }
            else
            {
                RichLogs("Okay", Color.LimeGreen, true);
            }
        }

        /// <summary>
        /// Logs an error with standard formatting
        /// </summary>
        /// <param name="message">Error message</param>
        public static void LogError(string message)
        {
            RichLogs(message, Color.IndianRed, true);
        }

        /// <summary>
        /// Logs a warning with standard formatting
        /// </summary>
        /// <param name="message">Warning message</param>
        public static void LogWarning(string message)
        {
            RichLogs(message, Color.Orange, true);
        }

        /// <summary>
        /// Logs information with standard formatting
        /// </summary>
        /// <param name="message">Information message</param>
        public static void LogInfo(string message)
        {
            RichLogs(message, Color.CornflowerBlue, true);
        }

        /// <summary>
        /// Executes a common ADB operation pattern with error handling
        /// </summary>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="operationFunc">The operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        public static async Task ExecuteWithStandardErrorHandlingAsync(
            string operationName,
            Func<CancellationToken, Task> operationFunc,
            CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await operationFunc(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                throw; // Re-throw cancellation exceptions
            }
            catch (Exception ex)
            {
                LogError($"Error in {operationName}: {ex.Message}");
                throw; // Re-throw to maintain existing error handling behavior
            }
        }

        /// <summary>
        /// Executes a function with standard error handling and returns result
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="operationFunc">The operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        public static async Task<T> ExecuteWithStandardErrorHandlingAsync<T>(
            string operationName,
            Func<CancellationToken, Task<T>> operationFunc,
            CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                return await operationFunc(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                throw; // Re-throw cancellation exceptions
            }
            catch (Exception ex)
            {
                LogError($"Error in {operationName}: {ex.Message}");
                throw; // Re-throw to maintain existing error handling behavior
            }
        }

        /// <summary>
        /// Executes FRP removal commands with standard pattern
        /// </summary>
        /// <param name="adbClient">ADB client instance</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        public static async Task ExecuteStandardFrpRemovalAsync(
            AndroidDebugBridge.AdbClient adbClient,
            CancellationToken cancellationToken = default)
        {
            await ExecuteWithStandardErrorHandlingAsync("FRP Removal", async (ct) =>
            {
                LogStep("Remove FRP...");
                await Task.Run(() =>
                {
                    adbClient.ExecuteRemoteCommand("am start -n com.google.android.gsf.login/");
                    adbClient.ExecuteRemoteCommand("am start -n com.google.android.gsf.login.LoginActivity");
                    adbClient.ExecuteRemoteCommand("am start -n com.sec.android.app.launcher/com.android.launcher2.Launcher");
                    adbClient.ExecuteRemoteCommand("content insert --uri content://settings/secure --bind name:s:user_setup_complete --bind value:s:1");
                }, ct);
                LogStep("Remove FRP...", false);
            }, cancellationToken);
        }

        /// <summary>
        /// Executes reboot command with standard pattern
        /// </summary>
        /// <param name="adbClient">ADB client instance</param>
        /// <param name="rebootMode">Reboot mode (e.g., "download", "recovery", or empty for normal reboot)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        public static async Task ExecuteStandardRebootAsync(
            AndroidDebugBridge.AdbClient adbClient,
            string rebootMode = "",
            CancellationToken cancellationToken = default)
        {
            var rebootCommand = string.IsNullOrEmpty(rebootMode) ? "reboot" : $"reboot {rebootMode}";
            var logMessage = string.IsNullOrEmpty(rebootMode) ? "Rebooting..." : $"Reboot to {rebootMode} mode...";

            await ExecuteWithStandardErrorHandlingAsync("Reboot", async (ct) =>
            {
                LogStep(logMessage);
                await Task.Run(() => adbClient.ExecuteRemoteCommand(rebootCommand), ct);
                LogStep(logMessage, false);
            }, cancellationToken);
        }
    }
}
