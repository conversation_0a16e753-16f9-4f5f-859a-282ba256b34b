﻿using Avalonia.Threading;
using SamsungTool.Library;
using SamsungTool.Library.AndroidDebugBridge;
using SamsungTool.Library.GUI;
using SamsungTool.Library.Security;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Sockets;
using System.Text;
using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations.Adb
{
    public abstract class AdbServiceBase : IDisposable
    {
        protected static readonly ConcurrentDictionary<string, Dictionary<string, string>> _devicePropertiesCache = new();
        protected static readonly ConcurrentDictionary<string, string> _imeiCache = new();
        protected static readonly ConcurrentDictionary<string, DateTime> _cacheTimestamps = new();
        protected static readonly TimeSpan CACHE_EXPIRY = TimeSpan.FromMinutes(5);
        protected static Timer? _cacheCleanupTimer;
        protected static bool _disposed = false;

        protected static readonly AdbClient ADB = new AdbClient();
        protected static string? SNADB { get; set; }
        protected static bool DebugNetwork { get; set; } = true;

        protected static void DebugConsole(string title, string? content) { }

        static AdbServiceBase()
        {
            _cacheCleanupTimer = new Timer(CleanupExpiredCache, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }

        private static void CleanupExpiredCache(object? state)
        {
            if (_disposed) return;

            try
            {
                var now = DateTime.Now;
                var expiredKeys = _cacheTimestamps
                    .Where(kvp => (now - kvp.Value) >= CACHE_EXPIRY)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _cacheTimestamps.TryRemove(key, out _);
                    _devicePropertiesCache.TryRemove(key, out _);
                    _imeiCache.TryRemove(key, out _);
                }
            }
            catch
            {
                // Silent error handling for production
            }
        }

        protected static Task<bool> CheckServerAsync()
        {
            try
            {
                using (var tcpClient = new TcpClient("127.0.0.1", 5037))
                {
                    return Task.FromResult(true);
                }
            }
            catch (SocketException)
            {
                return Task.FromResult(false);
            }
        }

        protected static Task<bool> InitializeAsync(CancellationToken cancellationToken = default)
        {
            return Task.Run(async () =>
            {
                RichLogs("Initializing protocol...", Color.Silver, false);

                cancellationToken.ThrowIfCancellationRequested();

                if (!await CheckServerAsync())
                {
                    if (!await Task.Run(() => ADB.StartServer(), cancellationToken))
                    {
                        RichLogs("Failed, ADB.exe not found!", Color.IndianRed, true);
                        return false;
                    }
                }

                RichLogs("Okay", Color.LimeGreen, true);
                return true;
            }, cancellationToken);
        }

        protected static Task<bool> FindDeviceAsync(CancellationToken cancellationToken = default)
        {
            return Task.Run(async () =>
            {
                // Update UI on UI thread
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    RichLogs("Connecting to device : ", Color.Silver, false);
                });

                cancellationToken.ThrowIfCancellationRequested();

                // Device discovery on background thread
                AdbDevice[]? devices = null;
                try
                {
                    devices = await Task.Run(() => ADB.GetDevices(), cancellationToken);
                }
                catch (Exception)
                {
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        RichLogs("Failed to get devices!", Color.IndianRed, true);
                    });
                    return false;
                }

                if (devices == null || devices.Length == 0)
                {
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        RichLogs("No device found!", Color.IndianRed, true);
                    });
                    return false;
                }

                // Update UI with device info on UI thread
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    foreach (var device in devices)
                    {
                        if (device != null)
                        {
                            RichLogs($"{device.SerialNumber?.ToUpper()}", Color.CornflowerBlue, true);
                            Console.WriteLine($"ADB  [ SN : {device.SerialNumber?.ToUpper()} - Model : {device.Model} ]");
                        }
                    }
                });

                SNADB = devices[0]?.SerialNumber;
                return true;
            }, cancellationToken);
        }

        protected static async Task<bool> ReadDeviceInfoAsync(CancellationToken cancellationToken = default)
        {
            return await Task.Run(async () =>
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    if (string.IsNullOrEmpty(SNADB))
                    {
                        RichLogs("No device selected", Color.IndianRed, true);
                        return false;
                    }

                    ADB.SetDevice(SNADB);
                    RichLogs("Reading device information...", Color.Silver, false);

                    using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    cts.CancelAfter(TimeSpan.FromSeconds(20));

                    try
                    {
                        var getPropTask = Task.Run(() =>
                        {
                            try
                            {
                                return ADB.ExecuteRemoteCommand("getprop");
                            }
                            catch (AdbException ex)
                            {
                                RichLogs($"ADB Error: {ex.Message}", Color.IndianRed, true);
                                throw;
                            }
                        }, cts.Token);

                        var imeiTask = Task.Run(() =>
                        {
                            try
                            {
                                const string imeiCmd =
                                    "sh -c \"service call iphonesubinfo 1 s16 com.android.shell | " +
                                    "cut -c 50-66 | tr -d \\'.[:space:]\\'\"";
                                return ADB.ExecuteRemoteCommand(imeiCmd);
                            }
                            catch
                            {
                                return Array.Empty<string>();
                            }
                        }, cts.Token);

                        var propOutput = await getPropTask;

                        if (propOutput == null)
                        {
                            RichLogs("Failed to get device properties", Color.IndianRed, true);
                            return false;
                        }

                        var props = new Dictionary<string, string>(propOutput.Length);
                        foreach (var line in propOutput)
                        {
                            if (string.IsNullOrEmpty(line)) continue;
                            
                            int i1 = line.IndexOf('[');
                            if (i1 < 0) continue;
                            int i2 = line.IndexOf(']', i1 + 1);
                            if (i2 < 0) continue;
                            int i3 = line.IndexOf('[', i2 + 1);
                            if (i3 < 0) continue;
                            int i4 = line.IndexOf(']', i3 + 1);
                            if (i4 < 0) continue;

                            string key = line.Substring(i1 + 1, i2 - i1 - 1);
                            string val = line.Substring(i3 + 1, i4 - i3 - 1);
                            props[key] = val;
                        }

                        var imeiOutput = await imeiTask;
                        string imei = "";
                        if (imeiOutput != null && imeiOutput.Length > 0)
                        {
                            imei = string.Concat(imeiOutput).Trim();
                        }

                        RichLogs("Okay", Color.LimeGreen, true);
                        RichLogs("", Color.Silver, true);

                        DisplayProperty("Model", GetPropValue(props, "ro.product.model"));
                        DisplayProperty("Brand", GetPropValue(props, "ro.product.brand"));
                        DisplayProperty("Carrier ID", GetPropValue(props, "ro.boot.carrierid"));
                        DisplayProperty("Sales Code", GetPropValue(props, "ro.csc.sales_code"));
                        DisplayProperty("Country Code", GetPropValue(props, "ro.csc.country_code"));
                        DisplayProperty("Timezone", GetPropValue(props, "persist.sys.timezone"));

                        var androidRel = GetPropValue(props, "ro.build.version.release");
                        if (!string.IsNullOrEmpty(androidRel))
                        {
                            var sdk = GetPropValue(props, "ro.build.version.sdk");
                            var buildId = GetPropValue(props, "ro.build.id");
                            var formatted = $"Android {androidRel}"
                                          + (!string.IsNullOrEmpty(sdk) ? $" [SDK {sdk}]" : "")
                                          + (!string.IsNullOrEmpty(buildId) ? $" [Build {buildId}]" : "");
                            DisplayProperty("Android Version", formatted);
                        }

                        var oneUi = GetPropValue(props, "ro.build.version.oneui");
                        if (!string.IsNullOrEmpty(oneUi) && oneUi.Length >= 3)
                        {
                            oneUi = oneUi.Replace("0", "");
                            int idx = oneUi.IndexOf('1');
                            if (idx >= 0) oneUi = oneUi.Insert(idx + 1, ".");
                            DisplayProperty("One UI Version", oneUi);
                        }

                        DisplayProperty("Build Date", GetPropValue(props, "ro.build.date"));
                        DisplayProperty("BL Version", GetPropValue(props, "ro.bootloader"));
                        DisplayProperty("PDA Version", GetPropValue(props, "ro.build.PDA"));
                        DisplayProperty("Phone Version", GetPropValue(props, "gsm.version.baseband"));
                        DisplayProperty("CSC Version", GetPropValue(props, "ro.omc.build.version"));
                        DisplayProperty("Board Platform", GetPropValue(props, "ro.board.platform"));
                        DisplayProperty("Modem Platform", GetPropValue(props, "ril.modem.board"));
                        DisplayProperty("Serial Number", GetPropValue(props, "ro.boot.serialno"));

                        if (!string.IsNullOrWhiteSpace(imei))
                            DisplayProperty("IMEI", imei);

                        DisplayProperty("Knox Version", GetPropValue(props, "ro.config.knox"));
                        DisplayProperty("Warranty Bit", GetPropValue(props, "ro.boot.warranty_bit"));
                        DisplayProperty("Em Did", GetPropValue(props, "ro.boot.em.did"));
                        DisplayProperty("SIM Status", GetPropValue(props, "gsm.sim.state"));
                        DisplayProperty("Multisim Config", GetPropValue(props, "persist.radio.multisim.config"));
                        DisplayProperty("Device State", GetPropValue(props, "knox.kg.state"));
                        DisplayProperty("Security Patch", GetPropValue(props, "ro.build.version.security_patch"));

                        return true;
                    }
                    catch (OperationCanceledException)
                    {
                        RichLogs("Operation timed out or was cancelled", Color.IndianRed, true);
                        return false;
                    }
                }
                catch (OperationCanceledException)
                {
                    RichLogs("Operation was cancelled", Color.Orange, true);
                    throw;
                }
                catch (AdbException ex)
                {
                    if (ex.Message.Contains("unauthorized"))
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("Device unauthorized", Color.IndianRed, true);
                        RichLogs("Please allow USB debugging", Color.IndianRed, true);
                    }
                    else
                    {
                        RichLogs($"ADB Error: {ex.Message}", Color.IndianRed, true);
                    }
                    return false;
                }
                catch (Exception ex)
                {
                    RichLogs($"Unexpected error: {ex.Message}", Color.IndianRed, true);
                    return false;
                }
            }, cancellationToken);
        }

        protected static string GetPropValue(Dictionary<string, string> props, string key)
        {
            return props.TryGetValue(key, out var val) && !string.IsNullOrWhiteSpace(val) ? val : "";
        }

        protected static void DisplayProperty(string label, string value)
        {
            if (!string.IsNullOrEmpty(value))
            {
                RichLogs($"{label.PadRight(15)} : ", Color.Silver, false);
                RichLogs(value, Color.CornflowerBlue, true);
            }
        }

        protected static string SanitizeShellParameter(string parameter)
        {
            if (string.IsNullOrEmpty(parameter))
                return string.Empty;

            return Regex.Replace(parameter, @"[^A-Za-z0-9_\-./]", "");
        }

        protected static Dictionary<string, List<string>> GroupSimilarCommands(string[] commands)
        {
            var result = new Dictionary<string, List<string>>();
            foreach (string cmd in commands)
            {
                if (string.IsNullOrEmpty(cmd)) continue;
                
                string key = "other";
                if (cmd.Contains("pm "))
                {
                    string[] parts = cmd.Split(new char[1] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length >= 4)
                    {
                        string action = parts[1];
                        string package = parts[^1];
                        key = action + "_" + package;
                    }
                }
                else if (cmd.Contains("settings put"))
                {
                    key = "settings";
                }
                else if (cmd.Contains("am "))
                {
                    string[] parts2 = cmd.Split(new char[1] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts2.Length >= 3)
                    {
                        key = "am_" + parts2[1];
                    }
                }
                if (!result.ContainsKey(key))
                {
                    result[key] = new List<string>();
                }
                result[key].Add(cmd);
            }
            return result;
        }

        protected static Dictionary<string, List<string>> GroupPermissionCommands(string[] commands)
        {
            var result = new Dictionary<string, List<string>>();
            foreach (string cmd in commands)
            {
                if (string.IsNullOrEmpty(cmd)) continue;
                
                string key = "other";
                if (cmd.Contains("pm revoke"))
                {
                    string[] parts = cmd.Split(new char[1] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length >= 4)
                    {
                        string permission = parts[3];
                        key = permission.StartsWith("android.") ? "android_permissions" : 
                              permission.StartsWith("com.samsung.") ? "samsung_permissions" : 
                              permission.StartsWith("com.sec.") ? "sec_permissions" : 
                              permission.StartsWith("com.google.") ? "google_permissions" : "other_permissions";
                    }
                }
                else if (cmd.Contains("settings put"))
                {
                    key = "settings";
                }
                else if (cmd.Contains("pm uninstall"))
                {
                    key = "uninstall";
                }
                if (!result.ContainsKey(key))
                {
                    result[key] = new List<string>();
                }
                result[key].Add(cmd);
            }
            return result;
        }

        protected static bool CheckDeviceCompatibility(Dictionary<string, string> props, string propKey,
            Func<string, bool> checkCondition, string errorMessage1, string errorMessage2)
        {
            string propValue = GetPropValue(props, propKey);
            if (checkCondition(propValue))
            {
                RichLogs("", Color.Silver, true);
                RichLogs(errorMessage1, Color.IndianRed, true);
                RichLogs(errorMessage2, Color.IndianRed, true);
                return false;
            }
            return true;
        }

        protected static async Task<byte[]?> GetBinaryFromServerAsync(string cpuArch, string jobName, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                var encryption = new AES256Encryption();
                var jsonElf = new JsonObject();

                string? imei = null;
                try
                {
                    if (_imeiCache.TryGetValue(SNADB ?? "", out var cachedImei) && !string.IsNullOrEmpty(cachedImei))
                    {
                        imei = cachedImei;
                    }
                    else
                    {
                        const string imeiCmd = "sh -c \"service call iphonesubinfo 1 s16 com.android.shell | " +
                                              "cut -c 50-66 | tr -d \\'.[:space:]\\'\"";
                        var imeiResult = ADB.ExecuteRemoteCommand(imeiCmd);
                        if (imeiResult != null && imeiResult.Length > 0)
                        {
                            var freshImei = string.Concat(imeiResult).Trim();
                            if (!string.IsNullOrWhiteSpace(freshImei))
                            {
                                imei = freshImei;
                                if (!string.IsNullOrEmpty(SNADB))
                                    _imeiCache[SNADB] = freshImei;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    RichLogs("Error reading device IMEI: " + ex.Message, Color.IndianRed, true);
                }

                if (string.IsNullOrEmpty(imei) || imei == "unknown" || imei.All(c => c == '0'))
                {
                    string? deviceCharacteristics = null;
                    try
                    {
                        const string characteristicsCmd = "getprop ro.build.characteristics";
                        var characteristicsResult = ADB.ExecuteRemoteCommand(characteristicsCmd);
                        if (characteristicsResult != null && characteristicsResult.Length > 0)
                        {
                            deviceCharacteristics = string.Concat(characteristicsResult).Trim().ToLowerInvariant();
                        }
                    }
                    catch
                    {
                        deviceCharacteristics = "phone";
                    }

                    if (!string.IsNullOrEmpty(deviceCharacteristics) && 
                        (deviceCharacteristics.Contains("tablet") || deviceCharacteristics == "tablet,nosdcard"))
                    {
                        imei = "";  
                    }
                    else
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("Invalid or missing IMEI detected", Color.IndianRed, true);
                        RichLogs("Please repair/restore your device's original Imei/Serial number to continue", Color.IndianRed, true);
                        return null;
                    }
                }

                string serialNumber = SNADB ?? "unknown";

                jsonElf["IMEI"] = imei;
                jsonElf["Serial"] = serialNumber;
                jsonElf["Job"] = jobName;
                jsonElf["Name"] = cpuArch;

                string jsonRequest = jsonElf.ToString();
                DebugConsole("DATA Request JSON (unencrypted)", jsonRequest);
                string encryptedRequest = encryption.Encrypt(jsonRequest);
                DebugConsole("DATA Request Payload (encrypted)", encryptedRequest);

                var requestBody = "{\"data\":\"" + encryptedRequest + "\"}";

                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {LoginWindow.AuthToken}");
                    var content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                    DebugConsole("HTTP POST", "https://samsungtool.service-app.org/api/user/data");
                    DebugConsole("HTTP POST Body", requestBody);
                    var response = await httpClient.PostAsync("https://samsungtool.service-app.org/api/user/data", content, cancellationToken);

                    if (!response.IsSuccessStatusCode)
                    {
                        RichLogs("Failed", Color.IndianRed, true);

                        try
                        {
                            string responseContent = await response.Content.ReadAsStringAsync();
                            DebugConsole("DATA Error Response (raw)", responseContent);
                            if (!string.IsNullOrEmpty(responseContent))
                            {
                                var errorJsonObj = JsonNode.Parse(responseContent);
                                if (errorJsonObj?["data"]?.GetValue<string>() is string errorDataStr && !string.IsNullOrEmpty(errorDataStr))
                                {
                                    string errorDecrypted = encryption.Decrypt(errorDataStr);
                                    DebugConsole("DATA Error Response (decrypted)", errorDecrypted);
                                    var errorData = JsonNode.Parse(errorDecrypted);

                                    if (errorData?["Message"]?.GetValue<string>() is string message)
                                    {
                                        RichLogs($"Server error ({response.StatusCode}): {message}", Color.IndianRed, true);
                                    }
                                }
                            }
                        }
                        catch
                        {
                            RichLogs($"Server error: {response.StatusCode}", Color.IndianRed, true);
                        }

                        return null;
                    }

                    var responseJson = await response.Content.ReadAsStringAsync();
                    DebugConsole("DATA Response (raw)", responseJson);
                    var responseObj = JsonNode.Parse(responseJson);

                    if (responseObj?["data"]?.GetValue<string>() is not string responseDataStr || string.IsNullOrEmpty(responseDataStr))
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("Invalid response from server", Color.IndianRed, true);
                        return null;
                    }

                    string decryptedResponse = encryption.Decrypt(responseDataStr);
                    DebugConsole("DATA Response (decrypted)", decryptedResponse);
                    var responseData = JsonNode.Parse(decryptedResponse);

                    if (responseData?["Status"]?.GetValue<bool>() == false)
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        if (responseData["Message"]?.GetValue<string>() is string message && !string.IsNullOrEmpty(message))
                        {
                            RichLogs($"Server message: {message}", Color.IndianRed, true);
                        }
                        return null;
                    }

                    if (responseData?["Data"]?.GetValue<string>() is not string dataStr || string.IsNullOrEmpty(dataStr))
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("No data received from server", Color.IndianRed, true);
                        return null;
                    }

                    return Convert.FromBase64String(dataStr);
                }
            }
            catch (OperationCanceledException)
            {
                RichLogs("Failed", Color.IndianRed, true);
                RichLogs("Server request was cancelled", Color.Orange, true);
                throw;
            }
            catch (Exception ex)
            {
                RichLogs("Failed", Color.IndianRed, true);
                RichLogs($"Error getting binary: {ex.Message}", Color.IndianRed, true);
                return null;
            }
        }

        protected static async Task<string?> GetAuthenticationSignatureAsync(string jobName, string challenge, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("\r\nRequesting authentication from server...", Color.Silver, false);

                var encryption = new AES256Encryption();
                var requestData = new JsonObject
                {
                    ["job"] = jobName,
                    ["challenge"] = challenge
                };
                string jsonRequest = requestData.ToString();
                DebugConsole("AUTH Request JSON (unencrypted)", jsonRequest);
                string encryptedRequest = encryption.Encrypt(jsonRequest);
                DebugConsole("AUTH Request Payload (encrypted)", encryptedRequest);
                var requestBody = "{\"data\":\"" + encryptedRequest + "\"}";
                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {LoginWindow.AuthToken}");

                    var content = new StringContent(requestBody,
                        Encoding.UTF8,
                        "application/json"
                    );
                    string authUrl = string.Equals(jobName, "KG_BYPASS_15", StringComparison.OrdinalIgnoreCase)
                        ? "https://samsungtool.service-app.org/api/user/challange"
                        : "https://samsungtool.service-app.org/api/user/auth";
                    DebugConsole("HTTP POST", authUrl);
                    DebugConsole("HTTP POST Body", requestBody);
                    var response = await httpClient.PostAsync(authUrl, content, cancellationToken);
                    if (!response.IsSuccessStatusCode)
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs($"Server error: {response.StatusCode}", Color.IndianRed, true);
                        try
                        {
                            var raw = await response.Content.ReadAsStringAsync();
                            DebugConsole("AUTH Error Response (raw)", raw);
                            var obj = JsonObject.Parse(raw);
                            var encData = obj?["data"]?.GetValue<string>();
                            if (!string.IsNullOrEmpty(encData))
                            {
                                var dec = encryption.Decrypt(encData);
                                DebugConsole("AUTH Error Response (decrypted)", dec);
                            }
                        }
                        catch { }
                        return null;
                    }

                    var responseJson = await response.Content.ReadAsStringAsync();
                    DebugConsole("AUTH Response (raw)", responseJson);
                    var responseObj = JsonObject.Parse(responseJson);

                    if (responseObj?["data"]?.GetValue<string>() is not string responseDataStr || string.IsNullOrEmpty(responseDataStr))
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("Invalid response from server", Color.IndianRed, true);
                        return null;
                    }

                    string decryptedResponse = encryption.Decrypt(responseDataStr);
                    DebugConsole("AUTH Response (decrypted)", decryptedResponse);
                    var responseData = JsonObject.Parse(decryptedResponse);

                    if (responseData?["Status"]?.GetValue<bool>() == false)
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        if (responseData["Message"]?.GetValue<string>() is string message)
                        {
                            RichLogs($"Server returned error: {message}", Color.IndianRed, true);
                        }
                        return null;
                    }

                    if (responseData?["Sign"]?.GetValue<string>() is not string sign || string.IsNullOrEmpty(sign))
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("Authentication signature not found in response", Color.IndianRed, true);
                        return null;
                    }

                    RichLogs("Okay", Color.LimeGreen, true);
                    DebugConsole("AUTH Sign (final)", sign);
                    return sign;
                }
            }
            catch (OperationCanceledException)
            {
                RichLogs("Authentication request was cancelled", Color.Orange, true);
                throw;
            }
            catch (Exception ex)
            {
                RichLogs("Failed", Color.IndianRed, true);
                RichLogs($"Auth error: {ex.Message}", Color.IndianRed, true);
                return null;
            }
        }

        /// <summary>
        /// Uploads a binary to the device and executes it via ADB shell, allowing custom output handling and logging.
        /// </summary>
        /// <param name="binaryData">The binary data to upload and execute.</param>
        /// <param name="remoteArgs">Arguments to pass to the binary on the device (can be null).</param>
        /// <param name="jobName">Job name for authentication signature (if needed).</param>
        /// <param name="outputHandler">Callback to handle each output line. Return true to break execution early.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>True if execution succeeded, false otherwise.</returns>
        protected static async Task<bool> ExecuteRemoteBinaryAsync(
            byte[] binaryData,
            string jobName,
            string? remoteArgs,
            Func<string, StreamWriter, Task<bool>>? outputHandler,
            CancellationToken cancellationToken = default)
        {
            string tempFileName = Path.GetRandomFileName();
            string tempFilePath = Path.Combine(Path.GetTempPath(), tempFileName);
            string remotePath = $"/data/local/tmp/{tempFileName}";
            bool executionSuccess = false;
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await File.WriteAllBytesAsync(tempFilePath, binaryData, cancellationToken);
                ADB.UploadFile(tempFilePath, remotePath, 755);

                using (var process = new Process())
                {
                    process.StartInfo.FileName = Path.Combine(Directory.GetCurrentDirectory(), "Data", "adb.exe");
                    process.StartInfo.Arguments = "shell";
                    process.StartInfo.RedirectStandardInput = true;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.CreateNoWindow = true;
                    process.Start();
                    ProcessManager.RegisterProcess(process);

                    using (var inputWriter = process.StandardInput)
                    using (var outputReader = process.StandardOutput)
                    {
                        if (inputWriter == null || outputReader == null)
                        {
                            RichLogs("Failed to initialize process streams", Color.IndianRed, true);
                            return false;
                        }

                        // Build command to execute binary
                        string execCmd = $"cd /data/local/tmp && chmod 755 {tempFileName} && ./{tempFileName}";
                        if (!string.IsNullOrWhiteSpace(remoteArgs))
                            execCmd += $" {remoteArgs}";
                        inputWriter.WriteLine(execCmd);
                        inputWriter.Flush();

                        char[] buffer = new char[4096];
                        int bytesRead;
                        while ((bytesRead = await outputReader.ReadAsync(buffer, 0, buffer.Length)) > 0)
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            string chunk = new string(buffer, 0, bytesRead);
                            string[] lines = chunk.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                            foreach (var line in lines)
                            {
                                if (string.IsNullOrWhiteSpace(line)) continue;
                                if (outputHandler != null)
                                {
                                    bool shouldBreak = await outputHandler(line, inputWriter);
                                    if (shouldBreak)
                                    {
                                        executionSuccess = true;
                                        break;
                                    }
                                }
                            }
                            if (executionSuccess) break;
                        }
                    }

                    try
                    {
                        if (!process.WaitForExit(10000))
                            process.Kill();
                    }
                    catch { }
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                RichLogs($"Execution error: {ex.Message}", Color.IndianRed, true);
                return false;
            }
            finally
            {
                try { if (File.Exists(tempFilePath)) File.Delete(tempFilePath); } catch { }
            }
            return executionSuccess;
        }

        /// <summary>
        /// Safely terminates a process with proper error handling to prevent InvalidOperationException
        /// </summary>
        /// <param name="process">The process to terminate</param>
        /// <param name="timeoutMs">Timeout in milliseconds for graceful termination</param>
        protected static void SafeTerminateProcess(Process process, int timeoutMs = 3000)
        {
            if (process == null)
                return;

            try
            {
                // Check if process is still accessible
                if (!process.HasExited)
                {
                    // Try graceful termination first
                    if (!process.WaitForExit(timeoutMs))
                    {
                        // Force kill if graceful termination fails
                        try
                        {
                            if (!process.HasExited)
                            {
                                process.Kill(true);
                                process.WaitForExit(2000); // Give it 2 seconds to terminate
                            }
                        }
                        catch (InvalidOperationException)
                        {
                            // Process already exited, ignore
                        }
                        catch (Exception ex)
                        {
                            RichLogs($"Warning: Failed to force terminate process: {ex.Message}", Color.Orange, true);
                        }
                    }
                }
            }
            catch (InvalidOperationException)
            {
                // Process already exited or disposed, ignore
            }
            catch (Exception ex)
            {
                RichLogs($"Warning: Process termination failed: {ex.Message}", Color.Orange, true);
            }
        }

        /// <summary>
        /// Safely checks if a process has exited without throwing InvalidOperationException
        /// </summary>
        /// <param name="process">The process to check</param>
        /// <returns>True if the process has exited or is no longer accessible, false otherwise</returns>
        protected static bool SafeHasExited(Process process)
        {
            if (process == null)
                return true;

            try
            {
                return process.HasExited;
            }
            catch (InvalidOperationException)
            {
                // Process is disposed or no longer accessible
                return true;
            }
            catch
            {
                // Any other exception, assume process is not accessible
                return true;
            }
        }

        public virtual void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                try
                {
                    _cacheCleanupTimer?.Dispose();
                    _cacheCleanupTimer = null;
                }
                catch
                {
                    // Silent error handling for production
                }

                try
                {
                    ADB?.Dispose();
                }
                catch
                {
                    // Silent error handling for production
                }
            }

            _disposed = true;
        }

        ~AdbServiceBase()
        {
            Dispose(false);
        }
    }
}
