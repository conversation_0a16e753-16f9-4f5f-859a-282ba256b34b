<Window x:Class="SamsungTool.Views.TokenExpiredWindow"
        xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Session Expired"
        Width="450"
        Height="280"
        WindowStartupLocation="CenterOwner"
        CanResize="False"
        SystemDecorations="None"
        Icon="avares://SamsungTool/Resources/logo.ico"
        Background="#1E1E1E"
        TransparencyLevelHint="AcrylicBlur">

    <Window.Styles>
        <Style Selector="TextBlock.header">
            <Setter Property="FontSize" Value="26"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#CC2E2E"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>

        <Style Selector="TextBlock.message">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#CCCCCC"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="22"/>
        </Style>

        <Style Selector="TextBlock.countdown">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Foreground" Value="#AAAAAA"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="FontStyle" Value="Italic"/>
        </Style>

        <Style Selector="Button.exit-button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#0099DD"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="0 12"/>
            <Setter Property="Height" Value="42"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style Selector="Button.exit-button:pointerover">
            <Setter Property="Background" Value="#0091EA"/>
            <Setter Property="BorderBrush" Value="#00CCFF"/>
        </Style>

        <Style Selector="Border.main-container">
            <Setter Property="Background" Value="#252526"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#404040"/>
        </Style>
    </Window.Styles>

    <Grid Margin="0">
        <Border Classes="main-container" Padding="40" Margin="20">
            <StackPanel Spacing="25" VerticalAlignment="Center">
                
                <!-- Header -->
                <TextBlock Text="SESSION EXPIRED" Classes="header"/>
                
                <!-- Message -->
                <TextBlock Name="messageText"
                           Classes="message"
                           Text="Your session has expired.&#x0a;The application will now close.&#x0a;&#x0a;Please restart and login again."/>

                <!-- Countdown -->
                <TextBlock Name="countdownText"
                           Classes="countdown"
                           Text="Closing automatically in 5 seconds..."/>

                <!-- OK Button -->
                <Button Name="btnOk"
                        Classes="exit-button"
                        Content="OK"
                        Width="140"
                        Margin="0 10 0 0"/>

            </StackPanel>
        </Border>
    </Grid>
</Window> 