﻿using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using Avalonia.Styling;
using System;
using System.Reflection;

namespace SamsungTool
{
    public partial class App : Application
    {
        private static readonly object _lockObject = new();
        private static MainWindow? _mainWindow;

        public static MainWindow? MainWindow
        {
            get
            {
                lock (_lockObject)
                {
                    return _mainWindow;
                }
            }
            private set
            {
                lock (_lockObject)
                {
                    _mainWindow = value;
                }
            }
        }

        public static Version? CurrentVersion { get; private set; }

        public override void Initialize()
        {
            AvaloniaXamlLoader.Load(this);
            RequestedThemeVariant = ThemeVariant.Dark;

            try
            {
                CurrentVersion = Assembly.GetExecutingAssembly().GetName().Version ?? new Version(1, 0, 0, 0);
            }
            catch
            {
                CurrentVersion = new Version(1, 0, 0, 0);
            }
        }

        public override void OnFrameworkInitializationCompleted()
        {
            if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                SetupDesktopLifetime(desktop);
            }

            base.OnFrameworkInitializationCompleted();
        }

        private static void SetupDesktopLifetime(IClassicDesktopStyleApplicationLifetime desktop)
        {
            var loginWindow = new LoginWindow();
            desktop.MainWindow = loginWindow;

            loginWindow.Closed += OnLoginWindowClosed;
            desktop.ShutdownRequested += OnShutdownRequested;
            desktop.Exit += OnApplicationExit;

            void OnLoginWindowClosed(object? sender, EventArgs e)
            {
                loginWindow.Closed -= OnLoginWindowClosed;

                try
                {
                    if (IsValidToken())
                    {
                        ShowMainWindow(desktop);
                    }
                    else
                    {
                        desktop.Shutdown();
                    }
                }
                catch
                {
                    desktop.Shutdown();
                }
                finally
                {
                    loginWindow.Dispose();
                }
            }

            void OnShutdownRequested(object? sender, ShutdownRequestedEventArgs e)
            {
                desktop.ShutdownRequested -= OnShutdownRequested;
                HandleApplicationShutdown();
            }

            void OnApplicationExit(object? sender, ControlledApplicationLifetimeExitEventArgs e)
            {
                desktop.Exit -= OnApplicationExit;
                HandleApplicationShutdown();
            }
        }

        private static bool IsValidToken()
        {
            return !string.IsNullOrEmpty(LoginWindow.AuthToken) &&
                   DateTime.UtcNow < LoginWindow.TokenExpiresAt;
        }

        private static void ShowMainWindow(IClassicDesktopStyleApplicationLifetime desktop)
        {
            MainWindow = new MainWindow();
            desktop.MainWindow = MainWindow;
            desktop.MainWindow.Show();
        }

        private static void HandleApplicationShutdown()
        {
            try
            {
                Program.OnApplicationShutdown();
            }
            catch
            {
            }
        }
    }
}