using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SamsungTool.Models;

namespace SamsungTool.Services.Interfaces
{
    /// <summary>
    /// Interface for log management operations
    /// </summary>
    public interface ILogManager
    {
        /// <summary>
        /// Start a new log session for an operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="operationType">Type of operation (MTP, ADB, etc.)</param>
        /// <returns>Log entry ID</returns>
        string StartLogSession(string operationName, string operationType);

        /// <summary>
        /// End a log session and save to file
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <param name="status">Final status of the operation</param>
        /// <param name="logContent">Complete log content</param>
        /// <returns>Path to saved log file</returns>
        Task<string> EndLogSessionAsync(string logId, LogEntryStatus status, string logContent);

        /// <summary>
        /// Update log content during operation
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <param name="additionalContent">Additional content to append</param>
        void UpdateLogContent(string logId, string additionalContent);

        /// <summary>
        /// Get all log entries from history
        /// </summary>
        /// <returns>Collection of log entries</returns>
        Task<LogEntryCollection> GetLogHistoryAsync();

        /// <summary>
        /// Get a specific log entry by ID
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <returns>Log entry or null if not found</returns>
        Task<LogEntry?> GetLogEntryAsync(string logId);

        /// <summary>
        /// Delete a log entry and its associated file
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteLogEntryAsync(string logId);

        /// <summary>
        /// Clear all log history
        /// </summary>
        /// <returns>True if cleared successfully</returns>
        Task<bool> ClearAllHistoryAsync();

        /// <summary>
        /// Get log content from file
        /// </summary>
        /// <param name="filePath">Path to log file</param>
        /// <returns>Log content</returns>
        Task<string> ReadLogFileAsync(string filePath);

        /// <summary>
        /// Export log history to a file
        /// </summary>
        /// <param name="exportPath">Path to export file</param>
        /// <returns>True if exported successfully</returns>
        Task<bool> ExportHistoryAsync(string exportPath);

        /// <summary>
        /// Get current active log session ID
        /// </summary>
        string? CurrentLogSessionId { get; }

        /// <summary>
        /// Event fired when a log session is completed
        /// </summary>
        event EventHandler<LogEntry>? LogSessionCompleted;
    }
}
