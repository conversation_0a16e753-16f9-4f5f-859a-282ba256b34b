﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SamsungTool.Library.Security
{
    public class HWIDGenerator
    {
        private static readonly string CustomSalt = "loideptrai";

        private static readonly string TransformedSalt = string.Join("",
            CustomSalt.Select(c => (char)(c ^ 0x5A)));

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool GetVolumeInformation(
            string? lpRootPathName,
            StringBuilder? lpVolumeNameBuffer,
            int nVolumeNameSize,
            out uint lpVolumeSerialNumber,
            out uint lpMaximumComponentLength,
            out uint lpFileSystemFlags,
            StringBuilder? lpFileSystemNameBuffer,
            int nFileSystemNameSize);

        [DllImport("kernel32.dll")]
        private static extern bool GetComputerName(StringBuilder lpBuffer, ref int lpnSize);

        [DllImport("advapi32.dll", SetLastError = true)]
        private static extern bool GetUserName(StringBuilder lpBuffer, ref int lpnSize);

        [DllImport("kernel32.dll")]
        private static extern void GetSystemInfo(out SYSTEM_INFO lpSystemInfo);

        [StructLayout(LayoutKind.Sequential)]
        public struct SYSTEM_INFO
        {
            public ushort processorArchitecture;
            public ushort reserved;
            public uint pageSize;
            public IntPtr minimumApplicationAddress;
            public IntPtr maximumApplicationAddress;
            public IntPtr activeProcessorMask;
            public uint numberOfProcessors;
            public uint processorType;
            public uint allocationGranularity;
            public ushort processorLevel;
            public ushort processorRevision;
        }

        private static readonly object _lock = new object();
        private static string? _persistentHWID = null;

        private static readonly byte[] AesKey = new byte[] {
            0xda, 0xfd, 0xd5, 0x75, 0xa6, 0xa0, 0xa5, 0x6e,
            0x8e, 0xd7, 0xc6, 0xf2, 0x81, 0x34, 0xa0, 0xdc,
            0x88, 0x7f, 0xe3, 0x69, 0x5c, 0x09, 0x28, 0xe7,
            0xa5, 0xd4, 0xc7, 0x79, 0xe0, 0xdf, 0xb6, 0x22
        };

        private static readonly byte[] AesIV = new byte[] {
            0xb6, 0xe6, 0xd7, 0x23, 0x59, 0x7d, 0xff, 0xb9,
            0xa4, 0x48, 0x31, 0x5f, 0xfd, 0x9e, 0xd6, 0x93
        };

        public static async Task<string> GenerateHWIDAsync()
        {
            lock (_lock)
            {
                if (!string.IsNullOrEmpty(_persistentHWID))
                    return _persistentHWID;

                var cached = LoadCachedHWIDSecure();
                if (!string.IsNullOrEmpty(cached))
                {
                    _persistentHWID = cached;
                    return _persistentHWID;
                }
            }

            var hwid = await Task.Run(GenerateNewHWID);

            lock (_lock)
            {
                _persistentHWID = hwid;
                SaveCachedHWIDSecure(hwid);
                return _persistentHWID;
            }
        }

        private static string GenerateNewHWID()
        {
            var cpuProcessorId = GetCpuProcessorId();
            var systemUUID = GetSystemUUID();
            var motherboardSerial = GetMotherboardSerial();
            var diskSerial = GetDiskSerial();

            var stableData = $"{cpuProcessorId}|{systemUUID}|{motherboardSerial}|{diskSerial}";

            return ComputeSHA256Hash(GetCustomSalt() + stableData + GetCustomSalt());
        }

        private static string GetCustomSalt()
        {
            return string.Join("", TransformedSalt.Select(c => (char)(c ^ 0x5A)));
        }

        private static string GetCpuProcessorId()
        {
            try
            {
                var result = ExecuteCommand("wmic", "cpu get ProcessorId /format:value");
                if (!string.IsNullOrEmpty(result))
                {
                    var lines = result.Split('\n');
                    foreach (var line in lines)
                    {
                        if (line.Contains("ProcessorId="))
                        {
                            var value = line.Split('=')[1].Trim();
                            if (!string.IsNullOrEmpty(value))
                                return value;
                        }
                    }
                }
            }
            catch { }

            try
            {
                GetSystemInfo(out SYSTEM_INFO sysInfo);
                return $"{sysInfo.processorType:X}{sysInfo.processorLevel:X}{sysInfo.processorRevision:X}";
            }
            catch { }

            return Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER") ?? "UNKNOWN_CPU";
        }

        private static string GetSystemUUID()
        {
            try
            {
                var result = ExecuteCommand("wmic", "csproduct get UUID /format:value");
                if (!string.IsNullOrEmpty(result))
                {
                    var lines = result.Split('\n');
                    foreach (var line in lines)
                    {
                        if (line.Contains("UUID="))
                        {
                            var value = line.Split('=')[1].Trim();
                            if (!string.IsNullOrEmpty(value) && value != "03000200-0400-0500-0006-000700080009")
                                return value.Replace("-", "");
                        }
                    }
                }
            }
            catch { }

            try
            {
#pragma warning disable CA1416 // Validate platform compatibility

                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Cryptography", false);
#pragma warning restore CA1416 // Validate platform compatibility

#pragma warning disable CA1416 // Validate platform compatibility
                var guid = key?.GetValue("MachineGuid")?.ToString();
#pragma warning restore CA1416 // Validate platform compatibility
                if (!string.IsNullOrEmpty(guid))
                    return guid.Replace("-", "");
            }
            catch { }

            return "UNKNOWN_UUID";
        }

        private static string GetMotherboardSerial()
        {
            try
            {
                var result = ExecuteCommand("wmic", "baseboard get SerialNumber /format:value");
                if (!string.IsNullOrEmpty(result))
                {
                    var lines = result.Split('\n');
                    foreach (var line in lines)
                    {
                        if (line.Contains("SerialNumber="))
                        {
                            var value = line.Split('=')[1].Trim();
                            if (!string.IsNullOrEmpty(value) &&
                                !value.Equals("Base Board Serial Number", StringComparison.OrdinalIgnoreCase) &&
                                !value.Equals("To be filled by O.E.M.", StringComparison.OrdinalIgnoreCase) &&
                                !value.Equals("Default string", StringComparison.OrdinalIgnoreCase))
                                return value;
                        }
                    }
                }
            }
            catch { }

            return "UNKNOWN_MB";
        }

        private static string GetDiskSerial()
        {
            try
            {
                var systemDrive = Path.GetPathRoot(Environment.SystemDirectory);
                if (!string.IsNullOrEmpty(systemDrive) && GetVolumeInformation(systemDrive, null, 0, out uint serialNumber, out _, out _, null, 0))
                {
                    return serialNumber.ToString("X8");
                }
            }
            catch { }

            try
            {
                var result = ExecuteCommand("wmic", "diskdrive get SerialNumber /format:value");
                if (!string.IsNullOrEmpty(result))
                {
                    var lines = result.Split('\n');
                    foreach (var line in lines)
                    {
                        if (line.Contains("SerialNumber="))
                        {
                            var value = line.Split('=')[1].Trim();
                            if (!string.IsNullOrEmpty(value))
                                return value;
                        }
                    }
                }
            }
            catch { }

            return "UNKNOWN_DISK";
        }

        private static string? ExecuteCommand(string fileName, string arguments)
        {
            try
            {
                using var process = new Process();
                process.StartInfo = new ProcessStartInfo
                {
                    FileName = fileName,
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                return process.ExitCode == 0 ? output : null;
            }
            catch
            {
                return null;
            }
        }

        private static string GenerateMachineFingerprint()
        {
            var components = new StringBuilder();

            components.Append(GetComputerNameDirect());
            components.Append(GetSystemInstallDate());
            components.Append(GetProcessorCount());
            components.Append(GetSystemArchitecture());

            using var sha = SHA256.Create();
            var hash = sha.ComputeHash(Encoding.UTF8.GetBytes(components.ToString()));
            return Convert.ToBase64String(hash);
        }

        private static string GetComputerNameDirect()
        {
            try
            {
                var buffer = new StringBuilder(256);
                int size = buffer.Capacity;
                if (GetComputerName(buffer, ref size))
                    return buffer.ToString();
            }
            catch { }
            return Environment.MachineName;
        }

        private static string GetSystemInstallDate()
        {
            try
            {
#pragma warning disable CA1416 // Validate platform compatibility
#pragma warning disable CA1416 // Validate platform compatibility
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion");
#pragma warning restore CA1416 // Validate platform compatibility
#pragma warning restore CA1416 // Validate platform compatibility
#pragma warning disable CA1416 // Validate platform compatibility
                var installDate = key?.GetValue("InstallDate")?.ToString();
#pragma warning restore CA1416 // Validate platform compatibility
                return installDate ?? "0";
            }
            catch { }
            return "0";
        }

        private static string GetProcessorCount()
        {
            return Environment.ProcessorCount.ToString();
        }

        private static string GetSystemArchitecture()
        {
            try
            {
                GetSystemInfo(out SYSTEM_INFO sysInfo);
                return $"{sysInfo.processorArchitecture}{sysInfo.numberOfProcessors}";
            }
            catch { }
            return Environment.Is64BitOperatingSystem ? "64" : "32";
        }

        private static void SaveCachedHWIDSecure(string hwid)
        {
            try
            {
                var machineFingerprint = GenerateMachineFingerprint();
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                var secureData = $"{hwid}|{machineFingerprint}|{timestamp}";

                var encryptedData = EncryptString(secureData);
                var integrityHash = ComputeIntegrityHash(secureData);

                var finalData = $"{encryptedData}|{integrityHash}";

                SaveToRandomLocations(finalData);
            }
            catch { }
        }

        private static void SaveToRandomLocations(string data)
        {
            var locations = new[]
            {
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), "drivers", "etc", $"hosts.{DateTime.Now.Ticks}.tmp"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "Microsoft", "Windows", "WER", $"temp.{Environment.TickCount}.dat"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Microsoft", "Windows", "INetCache", $"low.{Environment.WorkingSet}.cache"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Microsoft", "Windows", "Recent", $"recent.{Environment.ProcessorCount}.dat")
            };

            foreach (var location in locations.Take(3))
            {
                try
                {
                    var dir = Path.GetDirectoryName(location);
                    if (!string.IsNullOrEmpty(dir) && !Directory.Exists(dir))
                        Directory.CreateDirectory(dir);

                    if (!string.IsNullOrEmpty(location))
                    {
                    File.WriteAllText(location, data);
                    File.SetAttributes(location, FileAttributes.Hidden | FileAttributes.System | FileAttributes.Temporary);
                    }
                }
                catch { }
            }
        }

        private static string? LoadCachedHWIDSecure()
        {
            var possibleLocations = GetAllPossibleCacheLocations();

            foreach (var location in possibleLocations)
            {
                try
                {
                    if (string.IsNullOrEmpty(location) || !File.Exists(location))
                        continue;

                    var data = File.ReadAllText(location);
                    var parts = data.Split('|');

                    if (parts.Length != 2)
                        continue;

                    var decryptedData = DecryptString(parts[0]);
                    if (string.IsNullOrEmpty(decryptedData))
                        continue;

                    var dataParts = decryptedData.Split('|');
                    if (dataParts.Length != 3)
                        continue;

                    var hwid = dataParts[0];
                    var storedFingerprint = dataParts[1];
                    var timestamp = long.Parse(dataParts[2]);

                    var currentFingerprint = GenerateMachineFingerprint();
                    if (storedFingerprint != currentFingerprint)
                        continue;

                    var cacheAge = DateTimeOffset.UtcNow.ToUnixTimeSeconds() - timestamp;
                    if (cacheAge > 2592000)
                        continue;

                    return hwid;
                }
                catch { }
            }

            return null;
        }

        private static string[] GetAllPossibleCacheLocations()
        {
            var locations = new List<string>();

            var searchDirs = new[]
            {
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), "drivers", "etc"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "Microsoft", "Windows", "WER"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Microsoft", "Windows", "INetCache"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Microsoft", "Windows", "Recent")
            };

            foreach (var dir in searchDirs)
            {
                try
                {
                    if (!string.IsNullOrEmpty(dir) && Directory.Exists(dir))
                    {
                        var files = Directory.GetFiles(dir, "*.*", SearchOption.TopDirectoryOnly)
                            .Where(f => f.EndsWith(".tmp") || f.EndsWith(".dat") || f.EndsWith(".cache"))
                            .ToArray();

                        locations.AddRange(files);
                    }
                }
                catch { }
            }

            return locations.ToArray();
        }

        private static string ComputeSHA256Hash(string rawData)
        {
            using var sha256Hash = SHA256.Create();
            var bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(rawData));

            using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(GetCustomSalt())))
            {
                bytes = hmac.ComputeHash(bytes);
            }

            var builder = new StringBuilder();
            foreach (var b in bytes)
            {
                builder.Append(b.ToString("x2"));
            }
            return builder.ToString();
        }

        private static string ComputeIntegrityHash(string data)
        {
            string hashData = GetCustomSalt() + data + Environment.MachineName + GetCustomSalt();

            using (var sha = SHA256.Create())
            {
                byte[] hashBytes = sha.ComputeHash(Encoding.UTF8.GetBytes(hashData));

                for (int i = 0; i < hashBytes.Length; i++)
                {
                    hashBytes[i] ^= (byte)GetCustomSalt()[i % GetCustomSalt().Length];
                }

                return Convert.ToBase64String(hashBytes);
            }
        }

        private static string? EncryptString(string plainText)
        {
            try
            {
                using (Aes aes = Aes.Create())
                {
                    byte[] derivedKey = new byte[AesKey.Length];
                    Array.Copy(AesKey, derivedKey, AesKey.Length);

                    for (int i = 0; i < derivedKey.Length && i < GetCustomSalt().Length; i++)
                    {
                        derivedKey[i] ^= (byte)GetCustomSalt()[i];
                    }

                    aes.Key = derivedKey;
                    aes.IV = AesIV;
                    aes.Mode = CipherMode.CBC;

                    ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                    using (MemoryStream ms = new MemoryStream())
                    {
                        using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                        {
                            using (StreamWriter sw = new StreamWriter(cs))
                            {
                                sw.Write(plainText);
                            }
                        }
                        return Convert.ToBase64String(ms.ToArray());
                    }
                }
            }
            catch
            {
                return null;
            }
        }

        private static string? DecryptString(string cipherText)
        {
            try
            {
                byte[] cipherBytes = Convert.FromBase64String(cipherText);

                using (Aes aes = Aes.Create())
                {
                    byte[] derivedKey = new byte[AesKey.Length];
                    Array.Copy(AesKey, derivedKey, AesKey.Length);

                    for (int i = 0; i < derivedKey.Length && i < GetCustomSalt().Length; i++)
                    {
                        derivedKey[i] ^= (byte)GetCustomSalt()[i];
                    }

                    aes.Key = derivedKey;
                    aes.IV = AesIV;
                    aes.Mode = CipherMode.CBC;

                    ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                    using (MemoryStream ms = new MemoryStream(cipherBytes))
                    {
                        using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                        {
                            using (StreamReader sr = new StreamReader(cs))
                            {
                                return sr.ReadToEnd();
                            }
                        }
                    }
                }
            }
            catch
            {
                return null;
            }
        }
    }
}