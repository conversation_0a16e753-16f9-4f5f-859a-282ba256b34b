﻿using SamsungTool.Services.Interfaces;
using System;
using System.Drawing;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations
{
    public class DownloadService : IDownloadService, IDisposable
    {
        private bool _disposed = false;
        private static readonly HttpClient _httpClient = new HttpClient();
        public static string Server = "http://107.173.58.56:5020/";

        public async Task<byte[]?> DownloadAsync(string filename, CancellationToken cancellationToken = default)
        {
            string url = $"{Server}{filename}";
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                byte[] apk = await _httpClient.GetByteArrayAsync(url, cancellationToken);
                return apk;
            }
            catch (OperationCanceledException)
            {
                RichLogs("Download cancelled", Color.Orange, true);
                throw;
            }
            catch (HttpRequestException ex)
            {
                RichLogs($"{ex.Message}", Color.IndianRed, true);
                return null;
            }
            catch (Exception ex)
            {
                RichLogs($"Download error: {ex.Message}", Color.IndianRed, true);
                return null;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                // HttpClient should not be disposed here as it's static and shared
            }

            _disposed = true;
        }

        ~DownloadService()
        {
            Dispose(false);
        }
    }
}