using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Threading;

namespace SamsungTool.Views
{
    public static class UiAnimations
    {
        public static async Task AddLoadedAsync(Control? control, int delayAfterMs = 0)
        {
            if (control == null) return;
            await Dispatcher.UIThread.InvokeAsync(() => control.Classes.Add("loaded"));
            if (delayAfterMs > 0)
            {
                await Task.Delay(delayAfterMs);
            }
        }

        public static async Task AnimateCardWithHeaderAsync(Window window, string cardName, string headerName, int cardDelayMs = 100, int headerDelayMs = 50)
        {
            var card = window.FindControl<Border>(cardName);
            var header = window.FindControl<TextBlock>(headerName);
            await AddLoadedAsync(card, cardDelayMs);
            await AddLoadedAsync(header, headerDelayMs);
        }

        public static async Task AddLoadedToButtonsInUniformGridsAsync(Control? root, int delayBetweenMs)
        {
            foreach (var control in EnumerateDescendants(root))
            {
                if (control is UniformGrid ug)
                {
                    foreach (var child in ug.Children)
                    {
                        if (child is Button btn)
                        {
                            await AddLoadedAsync(btn, delayBetweenMs);
                        }
                    }
                }
            }
        }

        public static async Task AddLoadedDeepAsync(Control? root, int delayBetweenMs, Func<Control, bool> predicate)
        {
            foreach (var control in EnumerateDescendants(root))
            {
                if (predicate(control))
                {
                    await AddLoadedAsync(control, delayBetweenMs);
                }
            }
        }

        private static IEnumerable<Control> EnumerateDescendants(Control? root)
        {
            if (root == null) yield break;

            if (root is Panel panel)
            {
                foreach (var child in panel.Children)
                {
                    if (child is Control c)
                    {
                        yield return c;
                        foreach (var g in EnumerateDescendants(c))
                            yield return g;
                    }
                }
            }
            else if (root is Decorator decorator)
            {
                if (decorator.Child is Control c)
                {
                    yield return c;
                    foreach (var g in EnumerateDescendants(c))
                        yield return g;
                }
            }
            else if (root is ContentControl contentControl)
            {
                if (contentControl.Content is Control c)
                {
                    yield return c;
                    foreach (var g in EnumerateDescendants(c))
                        yield return g;
                }
            }
        }

        public static async Task AddLoadedSequentiallyAsync(IEnumerable<Control?> controls, int delayBetweenMs)
        {
            foreach (var c in controls)
            {
                await AddLoadedAsync(c, delayBetweenMs);
            }
        }

        public static async Task AddLoadedToChildrenAsync(Panel? panel, int delayBetweenMs, Func<Control, bool>? predicate = null)
        {
            if (panel == null) return;
            foreach (var child in panel.Children)
            {
                if (child is Control control)
                {
                    if (predicate == null || predicate(control))
                    {
                        await AddLoadedAsync(control, delayBetweenMs);
                    }
                }
            }
        }
    }
}


