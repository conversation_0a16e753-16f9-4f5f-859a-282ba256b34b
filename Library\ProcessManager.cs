using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Library;

public static class ProcessManager
{
    private enum ProcessRole
    {
        Default = 0,
        InteractiveTerminal = 1
    }

    private sealed class TrackedProcess
    {
        public WeakReference<Process> ProcessRef { get; }
        public string? OperationId { get; }
        public ProcessRole Role { get; set; }
        public DateTime StartedAtUtc { get; }

        public TrackedProcess(Process process, string? operationId, ProcessRole role)
        {
            ProcessRef = new WeakReference<Process>(process);
            OperationId = operationId;
            Role = role;
            StartedAtUtc = DateTime.UtcNow;
        }
    }

    private static readonly ConcurrentDictionary<int, TrackedProcess> _runningProcesses = new();
    private static readonly ConcurrentDictionary<string, CancellationTokenSource> _operationTokens = new();
    private static readonly object _lock = new();
    private static readonly TimeSpan _cleanupInterval = TimeSpan.FromSeconds(30);
    private static Timer? _cleanupTimer;
    private static bool _disposed;


    static ProcessManager()
    {
        _cleanupTimer = new Timer(_ => CleanupFinishedProcesses(), null, _cleanupInterval, _cleanupInterval);
    }

    public static async Task<(int exitCode, string stdOut, string stdErr)> RunProcessAsync(
        string fileName,
        string arguments,
        string workingDirectory,
        string operationId,
        CancellationToken cancellationToken)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ProcessManager));
        var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, GetOrCreateOperationCts(operationId).Token);

        var psi = new ProcessStartInfo
        {
            FileName = fileName,
            Arguments = arguments,
            WorkingDirectory = workingDirectory,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        using var process = new Process { StartInfo = psi, EnableRaisingEvents = true };

        RegisterProcess(process, operationId: operationId);

        var stdOutBuffer = new StringWriter();
        var stdErrBuffer = new StringWriter();

        process.OutputDataReceived += (_, e) => { if (e.Data is not null) stdOutBuffer.WriteLine(e.Data); };
        process.ErrorDataReceived += (_, e) => { if (e.Data is not null) stdErrBuffer.WriteLine(e.Data); };

        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();

        await process.WaitForExitAsync(cts.Token).ConfigureAwait(false);

        var exitCode = process.ExitCode;

        _runningProcesses.TryRemove(process.Id, out _);

        return (exitCode, stdOutBuffer.ToString(), stdErrBuffer.ToString());
    }

    public static CancellationToken GetOperationToken(string operationId)
        => GetOrCreateOperationCts(operationId).Token;

    public static void CancelOperation(string operationId)
    {
        if (_operationTokens.TryRemove(operationId, out var cts))
        {
            cts.Cancel();
            cts.Dispose();
        }
    }

    public static void RegisterProcess(Process process)
    {
        RegisterProcess(process, operationId: null, isInteractiveTerminal: false);
    }

    public static void RegisterProcess(Process process, string? operationId = null, bool isInteractiveTerminal = false)
    {
        if (process == null)
            return;

        var role = isInteractiveTerminal ? ProcessRole.InteractiveTerminal : ProcessRole.Default;

        _runningProcesses[process.Id] = new TrackedProcess(process, operationId, role);

        process.EnableRaisingEvents = true;

        process.Exited += (_, _) =>
        {
            _runningProcesses.TryRemove(process.Id, out _);
        };
    }

    public static void TerminateAllProcesses()
    {
        lock (_lock)
        {
            foreach (var kv in _runningProcesses.ToList())
            {
                try
                {
                    var tracked = kv.Value;
                    if (tracked.Role == ProcessRole.InteractiveTerminal)
                    {
                        // Never auto-kill interactive terminals
                        continue;
                    }

                    if (tracked.ProcessRef.TryGetTarget(out var proc) && proc != null)
                    {
                        try
                        {
                            // Attempt to terminate without pre-check to avoid first-chance exceptions
                            proc.Kill(true);
                            proc.WaitForExit(1000);
                        }
                        catch
                        {
                            // Ignore any errors during termination
                        }
                    }

                    // Note: Process disposal is handled by the caller's using statement
                    // We only terminate and remove from tracking here
                }
                catch (InvalidOperationException)
                {
                    // Process already disposed or inaccessible, ignore
                }
                catch (Exception ex)
                {
                    // Log termination errors for debugging
                    ErrorLogger.LogError("ProcessManager.TerminateAllProcesses", ex);
                }
                finally
                {
                    // Ensure removal from dictionary
                    _runningProcesses.TryRemove(kv.Key, out _);
                }
            }
        }
    }

    /// <summary>
    /// Marks a running process as an interactive terminal that should not be auto-killed.
    /// </summary>
    public static void MarkAsInteractiveTerminal(Process process)
    {
        if (process == null) return;
        if (_runningProcesses.TryGetValue(process.Id, out var tracked))
        {
            tracked.Role = ProcessRole.InteractiveTerminal;
        }
    }

    /// <summary>
    /// Returns true if the PID is tracked as an interactive terminal created by this application.
    /// </summary>
    public static bool IsInteractiveTerminalPid(int processId)
    {
        if (_runningProcesses.TryGetValue(processId, out var tracked))
        {
            return tracked.Role == ProcessRole.InteractiveTerminal && tracked.ProcessRef.TryGetTarget(out var p) && p != null;
        }
        return false;
    }

    /// <summary>
    /// Terminates all processes associated with the provided operation identifier.
    /// Interactive terminals are skipped.
    /// </summary>
    public static void TerminateOperationProcesses(string operationId)
    {
        if (string.IsNullOrWhiteSpace(operationId)) return;

        lock (_lock)
        {
            foreach (var kv in _runningProcesses.ToList())
            {
                var tracked = kv.Value;
                if (tracked.OperationId != operationId) continue;
                if (tracked.Role == ProcessRole.InteractiveTerminal) continue;

                try
                {
                    if (tracked.ProcessRef.TryGetTarget(out var proc) && proc != null)
                    {
                        try
                        {
                            proc.Kill(true);
                            proc.WaitForExit(1000);
                        }
                        catch
                        {
                        }
                    }
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError("ProcessManager.TerminateOperationProcesses", ex);
                }
                finally
                {
                    _runningProcesses.TryRemove(kv.Key, out _);
                }
            }
        }
    }

    private static CancellationTokenSource GetOrCreateOperationCts(string operationId)
        => _operationTokens.GetOrAdd(operationId, _ => new CancellationTokenSource());

    private static void CleanupFinishedProcesses()
    {
        if (_disposed) return;

        try
        {
            // Remove entries whose weak reference is dead
            foreach (var kv in _runningProcesses.ToList())
            {
                if (!kv.Value.ProcessRef.TryGetTarget(out var proc) || proc == null)
                {
                    _runningProcesses.TryRemove(kv.Key, out _);
                }
            }
        }
        catch (Exception ex)
        {
            // Never let timer callback surface exceptions
            ErrorLogger.LogError("ProcessManager.CleanupFinishedProcesses", ex);
        }
    }

    // Note: HasExited is intentionally not used in cleanup to avoid exceptions during debugging.

    /// <summary>
    /// Executes a shell command with standard error handling and logging
    /// </summary>
    /// <param name="command">Command to execute</param>
    /// <param name="arguments">Command arguments</param>
    /// <param name="workingDirectory">Working directory</param>
    /// <param name="operationId">Operation identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Command result with exit code and output</returns>
    public static async Task<(bool success, string output, string error)> ExecuteShellCommandAsync(
        string command,
        string arguments,
        string workingDirectory,
        string operationId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var (exitCode, stdOut, stdErr) = await RunProcessAsync(
                command, arguments, workingDirectory, operationId, cancellationToken);

            return (exitCode == 0, stdOut, stdErr);
        }
        catch (Exception ex)
        {
            ErrorLogger.LogError($"ProcessManager.ExecuteShellCommand.{operationId}", ex);
            return (false, string.Empty, ex.Message);
        }
    }

    /// <summary>
    /// Executes a command with timeout
    /// </summary>
    /// <param name="command">Command to execute</param>
    /// <param name="arguments">Command arguments</param>
    /// <param name="workingDirectory">Working directory</param>
    /// <param name="timeoutMs">Timeout in milliseconds</param>
    /// <param name="operationId">Operation identifier</param>
    /// <returns>Command result</returns>
    public static async Task<(bool success, string output, string error)> ExecuteWithTimeoutAsync(
        string command,
        string arguments,
        string workingDirectory,
        int timeoutMs,
        string operationId)
    {
        using var cts = new CancellationTokenSource(timeoutMs);
        return await ExecuteShellCommandAsync(command, arguments, workingDirectory, operationId, cts.Token);
    }

    public static void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        // Dispose cleanup timer
        _cleanupTimer?.Dispose();

        // Terminate and dispose all processes
        TerminateAllProcesses();

        // Dispose all operation tokens
        foreach (var cts in _operationTokens.Values)
        {
            try
            {
                cts.Dispose();
            }
            catch
            {
                // Ignore disposal errors
            }
        }
        _operationTokens.Clear();

        // Clear running processes dictionary
        _runningProcesses.Clear();
    }
}