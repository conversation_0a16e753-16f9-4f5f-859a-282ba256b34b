using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace SamsungTool.Library.Security
{
    public class AES256Encryption : IDisposable
    {
        private Aes _encryptor;
        private bool _disposed = false;

        public AES256Encryption()
        {
            _encryptor = Aes.Create();
        }

        public string Encrypt(string plainText)
        {
            byte[] key = new byte[] { 0xda, 0xfd, 0xd5, 0x75, 0xa6, 0xa0, 0xa5, 0x6e, 0x8e, 0xd7, 0xc6, 0xf2, 0x81, 0x34, 0xa0, 0xdc, 0x88, 0x7f, 0xe3, 0x69, 0x5c, 0x09, 0x28, 0xe7, 0xa5, 0xd4, 0xc7, 0x79, 0xe0, 0xdf, 0xb6, 0x22 };
            byte[] iv = new byte[] { 0xb6, 0xe6, 0xd7, 0x23, 0x59, 0x7d, 0xff, 0xb9, 0xa4, 0x48, 0x31, 0x5f, 0xfd, 0x9e, 0xd6, 0x93 };

            _encryptor.Mode = CipherMode.CBC;
            _encryptor.Key = key;
            _encryptor.IV = iv;

            using MemoryStream memoryStream = new MemoryStream();
            using ICryptoTransform aesEncryptor = _encryptor.CreateEncryptor();
            using CryptoStream cryptoStream = new CryptoStream(memoryStream, aesEncryptor, CryptoStreamMode.Write);

            byte[] plainBytes = Encoding.ASCII.GetBytes(plainText);
            cryptoStream.Write(plainBytes, 0, plainBytes.Length);
            cryptoStream.FlushFinalBlock();

            byte[] cipherBytes = memoryStream.ToArray();
            string cipherText = Convert.ToBase64String(cipherBytes, 0, cipherBytes.Length);
            return cipherText;
        }

        public string Decrypt(string cipherText)
        {
            byte[] key = new byte[] { 0xda, 0xfd, 0xd5, 0x75, 0xa6, 0xa0, 0xa5, 0x6e, 0x8e, 0xd7, 0xc6, 0xf2, 0x81, 0x34, 0xa0, 0xdc, 0x88, 0x7f, 0xe3, 0x69, 0x5c, 0x09, 0x28, 0xe7, 0xa5, 0xd4, 0xc7, 0x79, 0xe0, 0xdf, 0xb6, 0x22 };
            byte[] iv = new byte[] { 0xb6, 0xe6, 0xd7, 0x23, 0x59, 0x7d, 0xff, 0xb9, 0xa4, 0x48, 0x31, 0x5f, 0xfd, 0x9e, 0xd6, 0x93 };

            _encryptor.Mode = CipherMode.CBC;
            _encryptor.Key = key;
            _encryptor.IV = iv;

            using MemoryStream memoryStream = new MemoryStream();
            using ICryptoTransform aesDecryptor = _encryptor.CreateDecryptor();
            using CryptoStream cryptoStream = new CryptoStream(memoryStream, aesDecryptor, CryptoStreamMode.Write);
            string plainText = String.Empty;

            byte[] cipherBytes = Convert.FromBase64String(cipherText);
            cryptoStream.Write(cipherBytes, 0, cipherBytes.Length);
            cryptoStream.FlushFinalBlock();

            byte[] plainBytes = memoryStream.ToArray();
            plainText = Encoding.ASCII.GetString(plainBytes, 0, plainBytes.Length);

            return plainText;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _encryptor?.Dispose();
                }

                _disposed = true;
            }
        }

        ~AES256Encryption()
        {
            Dispose(false);
        }
    }
}