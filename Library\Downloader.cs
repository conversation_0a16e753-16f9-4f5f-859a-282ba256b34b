﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Library
{
    public class Downloader
    {
        public class Thread : IDisposable
        {
            private SerialPort? _port;
            private readonly SemaphoreSlim _portLock = new SemaphoreSlim(1, 1);
            private readonly List<string> _availablePorts = new List<string>();
            private string? _currentPort;
            private bool _disposed = false;

            public async Task<bool> FindAndSetDownloadMode(CancellationToken cancellationToken = default)
            {
                try
                {
                    await _portLock.WaitAsync(cancellationToken);
                    try
                    {
                        _availablePorts.Clear();
                        _availablePorts.AddRange(SerialPort.GetPortNames());

                        if (_availablePorts.Count == 0)
                            return false;

                        foreach (string port in _availablePorts)
                        {
                            if (await TryConnectToPort(port, cancellationToken))
                            {
                                _currentPort = port;
                                return true;
                            }
                        }

                        return false;
                    }
                    finally
                    {
                        _portLock.Release();
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in FindAndSetDownloadMode: {ex.Message}");
                    return false;
                }
            }

            private async Task<bool> TryConnectToPort(string portName, CancellationToken cancellationToken = default)
            {
                SerialPort? tempPort = null;
                try
                {
                    if (_port != null && _port.IsOpen)
                    {
                        _port.Close();
                        _port.Dispose();
                        _port = null;
                    }

                    tempPort = new SerialPort
                    {
                        PortName = portName,
                        BaudRate = 115200,
                        ReadTimeout = 3000,
                        WriteTimeout = 3000
                    };

                    tempPort.Open();

                    if (!tempPort.IsOpen)
                        return false;

                    tempPort.DiscardInBuffer();
                    tempPort.DiscardOutBuffer();

                    await WriteToPortAsync(tempPort, "ODIN", cancellationToken);
                    await Task.Delay(500, cancellationToken);

                    if (tempPort.BytesToRead > 0)
                    {
                        string response = await ReadFromPortAsync(tempPort, cancellationToken);
                        if (response.Contains("LOKE"))
                        {
                            _port = tempPort;
                            tempPort = null;
                            return true;
                        }
                    }

                    return false;
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch
                {
                    return false;
                }
                finally
                {
                    if (tempPort != null)
                    {
                        try
                        {
                            if (tempPort.IsOpen)
                                tempPort.Close();
                            tempPort.Dispose();
                        }
                        catch
                        {
                            // Ignore cleanup errors
                        }
                    }
                }
            }

            public async Task<bool> ReadBytes(string address, CancellationToken cancellationToken = default)
            {
                try
                {
                    await _portLock.WaitAsync(cancellationToken);
                    try
                    {
                        if (_port == null || !_port.IsOpen)
                            return false;

                        _port.DiscardInBuffer();
                        _port.DiscardOutBuffer();

                        string command = $"READ:{address}";
                        await WriteToPortAsync(_port, command, cancellationToken);
                        await Task.Delay(1000, cancellationToken);

                        if (_port.BytesToRead > 0)
                        {
                            string response = await ReadFromPortAsync(_port, cancellationToken);
                            return response.Contains("ACK");
                        }

                        return false;
                    }
                    finally
                    {
                        _portLock.Release();
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in ReadBytes: {ex.Message}");
                    return false;
                }
            }

            public async Task<bool> WriteBytes(string data, CancellationToken cancellationToken = default)
            {
                try
                {
                    await _portLock.WaitAsync(cancellationToken);
                    try
                    {
                        if (_port == null || !_port.IsOpen)
                            return false;

                        _port.DiscardInBuffer();
                        _port.DiscardOutBuffer();

                        string command = $"WRITE:{data}";
                        await WriteToPortAsync(_port, command, cancellationToken);
                        await Task.Delay(1000, cancellationToken);

                        if (_port.BytesToRead > 0)
                        {
                            string response = await ReadFromPortAsync(_port, cancellationToken);
                            return response.Contains("ACK");
                        }

                        return false;
                    }
                    finally
                    {
                        _portLock.Release();
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in WriteBytes: {ex.Message}");
                    return false;
                }
            }

            public async Task<Dictionary<string, string>> Print(CancellationToken cancellationToken = default)
            {
                var result = new Dictionary<string, string>();

                try
                {
                    await _portLock.WaitAsync(cancellationToken);
                    try
                    {
                        if (_port == null || !_port.IsOpen)
                            return result;

                        _port.DiscardInBuffer();
                        _port.DiscardOutBuffer();

                        await WriteToPortAsync(_port, "DVIF", cancellationToken);
                        await Task.Delay(2000, cancellationToken);

                        if (_port.BytesToRead > 0)
                        {
                            string response = await ReadFromPortAsync(_port, cancellationToken);

                            if (response.Contains(";"))
                            {
                                string[] parts = response.Split(';');
                                foreach (string part in parts)
                                {
                                    if (part.Contains("="))
                                    {
                                        string[] keyValue = part.Split('=', 2);
                                        if (keyValue.Length == 2)
                                        {
                                            result[keyValue[0]] = keyValue[1];
                                        }
                                    }
                                }
                            }
                        }

                        return result;
                    }
                    finally
                    {
                        _portLock.Release();
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in Print: {ex.Message}");
                    return result;
                }
            }

            private async Task WriteToPortAsync(SerialPort port, string data, CancellationToken cancellationToken)
            {
                await Task.Run(() =>
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    port.Write(data);
                }, cancellationToken);
            }

            private async Task<string> ReadFromPortAsync(SerialPort port, CancellationToken cancellationToken)
            {
                return await Task.Run(() =>
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    return port.ReadExisting();
                }, cancellationToken);
            }

            public void Dispose()
            {
                Dispose(true);
                GC.SuppressFinalize(this);
            }

            protected virtual void Dispose(bool disposing)
            {
                if (_disposed)
                    return;

                if (disposing)
                {
                    try
                    {
                        if (_port != null)
                        {
                            if (_port.IsOpen)
                                _port.Close();
                            _port.Dispose();
                            _port = null;
                        }
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }

                    try
                    {
                        _portLock?.Dispose();
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }

                _disposed = true;
            }

            ~Thread()
            {
                Dispose(false);
            }
        }
    }
}