using SamsungTool.Library;
using SamsungTool.Library.GUI;
using SamsungTool.Library.Security;
using System;
using System.Drawing;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbChangeCSCService : AdbServiceBase
    {
        public async Task Run(CancellationToken cancellationToken = default)
        {
            UI.ClearLog();
            RichLogs("Operation: Change CSC [ 06 - 2025 ] [ ADB ]", Color.Silver, true);

            cancellationToken.ThrowIfCancellationRequested();
            if (!await InitializeAsync(cancellationToken))
            {
                RichLogs("Initializing protocol...Failed", Color.IndianRed, true);
                return;
            }

            if (!await FindDeviceAsync(cancellationToken))
            {
                RichLogs("No device found", Color.IndianRed, true);
                return;
            }

            if (!await ReadDeviceInfoAsync(cancellationToken))
            {
                RichLogs("Reading device information...Failed", Color.IndianRed, true);
                return;
            }

            await ExecuteCSCChangeProcess(cancellationToken);
        }

        private async Task ExecuteCSCChangeProcess(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await Task.Run(() => ADB.ExecuteRemoteCommand("svc wifi disable"), cancellationToken);
                //await Task.Run(() => ADB.ExecuteRemoteCommand("setprop ctl.start bootanim"), cancellationToken);

                RichLogs("Installing required components...", Color.Silver, false);
                bool apkInstalled = await ADB.InstallRetailsApk(cancellationToken);
                if (!apkInstalled)
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    RichLogs("Required component installation failed", Color.IndianRed, true);
                    return;
                }
                RichLogs("Okay", Color.LimeGreen, true);

                ProcessBar1(20);

                RichLogs("Detecting CPU architecture...", Color.Silver, false);
                string[]? cpuResult = ADB.ExecuteRemoteCommand("getprop ro.product.cpu.abi");
                string? cpuArch = (cpuResult != null && cpuResult.Length > 0) ? cpuResult[0].Trim() : null;
                if (string.IsNullOrEmpty(cpuArch))
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    RichLogs("Could not detect CPU architecture", Color.IndianRed, true);
                    return;
                }
                RichLogs($"Okay ({cpuArch})", Color.LimeGreen, true);

                ProcessBar1(40);

                RichLogs("Requesting data from server...", Color.Silver, false);
                byte[]? binaryData = await GetCSCChangeBinaryAsync(cpuArch, cancellationToken);
                if (binaryData == null)
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    return;
                }
                RichLogs("Okay", Color.LimeGreen, true);

                ProcessBar1(60);

                if (!await ExecuteCSCBinaryAsync(binaryData, cancellationToken))
                {
                    return;
                }

                ProcessBar1(80);
                ProcessBar1(100);
                RichLogs("Please select your desired CSC in the device settings and click install to complete the process.", Color.Goldenrod, true);
                RichLogs("Operation Completed", Color.LimeGreen, true);
            }
            catch (OperationCanceledException)
            {
                RichLogs("Operation was cancelled", Color.Orange, true);
                throw;
            }
            catch (Exception ex)
            {
                RichLogs("Failed", Color.IndianRed, true);
                RichLogs($"Error: {ex.Message}", Color.IndianRed, true);
            }
        }

        private async Task<byte[]?> GetCSCChangeBinaryAsync(string cpuArch, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                var encryption = new AES256Encryption();
                var jsonElf = new JsonObject();

                jsonElf["Job"] = "CSC_RETAILS";
                jsonElf["Name"] = cpuArch;
                string jsonRequest = jsonElf.ToString();
                string encryptedRequest = encryption.Encrypt(jsonRequest);

                var requestPayload = new JsonObject
                {
                    ["data"] = encryptedRequest
                };

                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {LoginWindow.AuthToken}");
                    var content = new StringContent(requestPayload.ToString(), Encoding.UTF8, "application/json");
                    var response = await httpClient.PostAsync("https://samsungtool.service-app.org/api/user/kg", content, cancellationToken);

                    if (!response.IsSuccessStatusCode)
                    {
                        RichLogs("Failed", Color.IndianRed, true);

                        try
                        {
                            string responseContent = await response.Content.ReadAsStringAsync();
                            if (!string.IsNullOrEmpty(responseContent))
                            {
                                var errorJsonObj = JsonNode.Parse(responseContent);
                                if (errorJsonObj != null && errorJsonObj["data"] != null &&
                                    !string.IsNullOrEmpty(errorJsonObj["data"]?.GetValue<string>()))
                                {
                                    string errorDecrypted = encryption.Decrypt(errorJsonObj["data"]!.GetValue<string>());
                                    var errorData = JsonNode.Parse(errorDecrypted);

                                    if (errorData != null && errorData["Message"] != null)
                                    {
                                        RichLogs($"Server error ({response.StatusCode}): {errorData["Message"]?.GetValue<string>()}",
                                            Color.IndianRed, true);
                                    }
                                }
                            }
                        }
                        catch
                        {
                            RichLogs($"Server error: {response.StatusCode}", Color.IndianRed, true);
                        }

                        return null;
                    }

                    var responseJson = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonNode.Parse(responseJson);

                    if (responseObj == null || responseObj["data"] == null ||
                        string.IsNullOrEmpty(responseObj["data"]?.GetValue<string>()))
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("Invalid response from server", Color.IndianRed, true);
                        return null;
                    }

                    string decryptedResponse = encryption.Decrypt(responseObj["data"]!.GetValue<string>());
                    var responseData = JsonNode.Parse(decryptedResponse);

                    if (responseData == null || responseData["Status"]?.GetValue<bool>() == false)
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        if (responseData != null && responseData["Message"] != null &&
                            !string.IsNullOrEmpty(responseData["Message"]?.GetValue<string>()))
                        {
                            RichLogs($"Server message: {responseData["Message"]?.GetValue<string>()}", Color.IndianRed, true);
                        }
                        return null;
                    }

                    if (responseData["Data"] == null || string.IsNullOrEmpty(responseData["Data"]?.GetValue<string>()))
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("No data received from server", Color.IndianRed, true);
                        return null;
                    }

                    return Convert.FromBase64String(responseData["Data"]!.GetValue<string>());
                }
            }
            catch (OperationCanceledException)
            {
                RichLogs("Server request was cancelled", Color.Orange, true);
                throw;
            }
            catch (Exception ex)
            {
                RichLogs("Failed", Color.IndianRed, true);
                RichLogs($"Error getting binary: {ex.Message}", Color.IndianRed, true);
                return null;
            }
        }

        private async Task<bool> ExecuteCSCBinaryAsync(byte[] binaryData, CancellationToken cancellationToken = default)
        {
            bool authCompleted = false;
            bool changeStarted = false;
            bool done = false;
            string jobName = "CSC_RETAILS";
            return await ExecuteRemoteBinaryAsync(
                binaryData,
                jobName,
                null,
                async (line, inputWriter) =>
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        if (line.Contains("[AUTH] Request:") && !authCompleted)
                        {
                            if (!changeStarted)
                            {
                                RichLogs("Applying CSC configuration...", Color.Silver, false);
                                changeStarted = true;
                            }
                            int startIndex = line.IndexOf("[AUTH] Request:") + "[AUTH] Request:".Length;
                            string authToken = line.Substring(startIndex).Trim();
                            string? signature = await GetAuthenticationSignatureAsync(jobName, authToken, cancellationToken);
                            if (string.IsNullOrEmpty(signature))
                            {
                                RichLogs("Failed", Color.IndianRed, true);
                                return true;
                            }
                            RichLogs("Verifying CSC configuration request...", Color.Silver, false);
                            inputWriter.WriteLine(signature);
                            inputWriter.Flush();
                            authCompleted = true;
                        }
                        if (line.Contains("[AUTH] OK!"))
                        {
                            if (changeStarted)
                                RichLogs("Okay", Color.LimeGreen, true);
                                RichLogs("Waiting for device to finish CSC change...", Color.Silver, false);
                        }
                        else if (line.Contains("Done!") || line.Contains("Done!!!"))
                        {
                            if (changeStarted)
                                RichLogs("Okay", Color.LimeGreen, true);
                            done = true;
                            return true;
                        }
                    }
                    return done;
                },
                cancellationToken
            );
        }
    }
}