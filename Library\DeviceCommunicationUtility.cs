using Avalonia.Threading;
using SamsungTool.Library.GUI;
using System;
using System.Drawing;
using System.IO.Ports;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Library
{
    /// <summary>
    /// Common device communication utilities to reduce code duplication across services
    /// Maintains AOT compatibility and preserves existing error handling patterns
    /// </summary>
    public static class DeviceCommunicationUtility
    {
        /// <summary>
        /// Gets the selected port name from the UI combo box with standard error handling
        /// </summary>
        /// <returns>Port name if successful, null if no device selected</returns>
        public static async Task<string?> GetSelectedPortNameAsync()
        {
            string? portName = null;

            await Dispatcher.UIThread.InvokeAsync(() =>
            {
                if (MainWindow.GUI?.cmbCOM?.SelectedItem == null)
                {
                    RichLogs("No Device selected!", Color.IndianRed, true);
                    return;
                }

                if (MainWindow.GUI.cmbCOM.SelectedItem is USB.COMINFO comInfo)
                {
                    portName = comInfo.Name;
                }
                else if (MainWindow.GUI.cmbCOM.SelectedItem is string selectedText)
                {
                    var match = Regex.Match(selectedText, @"\((COM\d+)\)");
                    if (match.Success)
                    {
                        portName = match.Groups[1].Value;
                    }
                }
            });

            return portName;
        }

        /// <summary>
        /// Logs device waiting and connection status with standard formatting
        /// </summary>
        /// <param name="portName">Port name to display</param>
        public static void LogDeviceConnection(string portName)
        {
            RichLogs($"{portName}", Color.CornflowerBlue, true);
            RichLogs($"Connecting to ", Color.Silver, false);
            RichLogs($"{portName}...", Color.CornflowerBlue, false);
            RichLogs("Okay", Color.LimeGreen, true);
        }

        /// <summary>
        /// Logs waiting for device with standard formatting
        /// </summary>
        public static void LogWaitingForDevice()
        {
            RichLogs("Waiting for device...", Color.Silver, false);
        }

        /// <summary>
        /// Validates port name and logs error if invalid
        /// </summary>
        /// <param name="portName">Port name to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool ValidatePortName(string? portName)
        {
            if (string.IsNullOrEmpty(portName))
            {
                RichLogs("Failed!", Color.IndianRed, true);
                return false;
            }
            return true;
        }

        /// <summary>
        /// Executes a serial port operation with standard error handling
        /// </summary>
        /// <param name="portName">Port name</param>
        /// <param name="operation">Operation to execute with the serial port</param>
        /// <param name="baudRate">Baud rate (default: 115200)</param>
        /// <param name="timeout">Timeout in milliseconds (default: 3000)</param>
        /// <returns>Result of the operation</returns>
        public static async Task<T?> ExecuteSerialPortOperationAsync<T>(
            string portName,
            Func<SerialPort, Task<T?>> operation,
            int baudRate = 115200,
            int timeout = 3000) where T : class
        {
            SerialPort? serialPort = null;
            try
            {
                serialPort = new SerialPort(portName, baudRate)
                {
                    ReadTimeout = timeout,
                    WriteTimeout = timeout
                };

                serialPort.Open();
                if (!serialPort.IsOpen)
                {
                    RichLogs("Failed to open port", Color.IndianRed, true);
                    return null;
                }

                return await operation(serialPort);
            }
            catch (UnauthorizedAccessException)
            {
                RichLogs("Port cannot be accessed, may be in use by another application", Color.IndianRed, true);
                return null;
            }
            catch (Exception ex)
            {
                RichLogs($"Error: {ex.Message}", Color.IndianRed, true);
                return null;
            }
            finally
            {
                try
                {
                    serialPort?.Close();
                    serialPort?.Dispose();
                }
                catch (Exception ex)
                {
                    // Log disposal errors silently to avoid affecting user experience
                    ErrorLogger.LogError("DeviceCommunicationUtility.ExecuteSerialPortOperation.Dispose", ex);
                }
            }
        }

        /// <summary>
        /// Writes data to a serial port with standard error handling
        /// </summary>
        /// <param name="port">Serial port</param>
        /// <param name="data">Data to write</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the write operation</returns>
        public static async Task WriteToPortAsync(SerialPort port, string data, CancellationToken cancellationToken = default)
        {
            await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                port.Write(data);
            }, cancellationToken);
        }

        /// <summary>
        /// Reads response from serial port with timeout and cancellation support
        /// </summary>
        /// <param name="port">Serial port</param>
        /// <param name="timeoutMs">Timeout in milliseconds</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <param name="terminators">Response terminators to look for</param>
        /// <returns>Response string or null if timeout/error</returns>
        public static async Task<string?> ReadFromPortAsync(
            SerialPort port,
            int timeoutMs = 3000,
            CancellationToken cancellationToken = default,
            params string[] terminators)
        {
            var response = new System.Text.StringBuilder();
            var buffer = new byte[1024];
            
            using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(timeoutMs);

            try
            {
                while (!cts.Token.IsCancellationRequested)
                {
                    if (port.BytesToRead > 0)
                    {
                        int bytesToRead = Math.Min(buffer.Length, port.BytesToRead);
                        int bytesRead = await port.BaseStream.ReadAsync(buffer, 0, bytesToRead, cts.Token);

                        if (bytesRead > 0)
                        {
                            string data = System.Text.Encoding.ASCII.GetString(buffer, 0, bytesRead);
                            response.Append(data);

                            // Check for terminators
                            string responseStr = response.ToString();
                            foreach (string terminator in terminators)
                            {
                                if (responseStr.Contains(terminator))
                                {
                                    return responseStr;
                                }
                            }
                        }
                    }
                    else
                    {
                        await Task.Delay(50, cts.Token);
                    }
                }

                return response.ToString();
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                return "CANCELLED";
            }
            catch (OperationCanceledException)
            {
                return response.ToString(); // Timeout
            }
            catch (Exception ex)
            {
                RichLogs($"Error reading from port: {ex.Message}", Color.IndianRed, true);
                return null;
            }
        }

        /// <summary>
        /// Sanitizes shell parameters to prevent injection attacks
        /// </summary>
        /// <param name="parameter">Parameter to sanitize</param>
        /// <returns>Sanitized parameter</returns>
        public static string SanitizeShellParameter(string parameter)
        {
            if (string.IsNullOrEmpty(parameter))
                return string.Empty;

            // Remove potentially dangerous characters
            return Regex.Replace(parameter, @"[;&|`$(){}[\]\\""']", "");
        }

        /// <summary>
        /// Logs operation result with standard formatting
        /// </summary>
        /// <param name="success">Whether operation was successful</param>
        /// <param name="successMessage">Message to show on success (default: "Okay")</param>
        /// <param name="failureMessage">Message to show on failure (default: "Failed!")</param>
        public static void LogOperationResult(bool success, string successMessage = "Okay", string failureMessage = "Failed!")
        {
            if (success)
            {
                RichLogs(successMessage, Color.LimeGreen, true);
            }
            else
            {
                RichLogs(failureMessage, Color.IndianRed, true);
            }
        }
    }
}
