using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using SamsungTool.Library.Security;
using SamsungTool.Services.Interfaces;

namespace SamsungTool.Services.Implementations
{
    internal class LicenseService : ILicenseService, IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly AES256Encryption _encryption;
        private bool _disposed = false;

        public LicenseService()
        {
            _httpClient = new HttpClient();
            _encryption = new AES256Encryption();
        }

        public async Task<LicenseInfo?> GetLicenseInfoAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(LoginWindow.AuthToken))
                {
                    return null;
                }

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {LoginWindow.AuthToken}");

                var response = await _httpClient.GetAsync("https://samsungtool.service-app.org/api/user/license");
                
                if (!response.IsSuccessStatusCode)
                {
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (string.IsNullOrEmpty(responseContent))
                {
                    return null;
                }

                // Parse the response to get the encrypted data
                using var document = JsonDocument.Parse(responseContent);
                var encryptedData = document.RootElement.GetProperty("data").GetString();
                
                if (string.IsNullOrEmpty(encryptedData))
                {
                    return null;
                }

                // Decrypt the data
                var decryptedJson = _encryption.Decrypt(encryptedData);
                
                if (string.IsNullOrEmpty(decryptedJson))
                {
                    return null;
                }

                // Parse the decrypted JSON
                using var decryptedDocument = JsonDocument.Parse(decryptedJson);
                var root = decryptedDocument.RootElement;

                var licenseInfo = new LicenseInfo
                {
                    Status = root.TryGetProperty("Status", out var statusProp) && statusProp.GetBoolean(),
                    Message = root.TryGetProperty("Message", out var messageProp) && messageProp.ValueKind != JsonValueKind.Null 
                        ? messageProp.GetString() : null,
                    Date = root.TryGetProperty("Date", out var dateProp) && dateProp.ValueKind != JsonValueKind.Null 
                        ? dateProp.GetString() : null
                };

                // Parse the expiration date
                if (!string.IsNullOrEmpty(licenseInfo.Date) && DateTime.TryParse(licenseInfo.Date, out var expirationDate))
                {
                    licenseInfo.ExpirationDate = expirationDate;
                }

                // Get username from token or other source if available
                licenseInfo.Username = GetUsernameFromToken();

                return licenseInfo;
            }
            catch (Exception)
            {
                return null;
            }
        }

        private string? GetUsernameFromToken()
        {
            return LoginWindow.SavedUsername ?? "User";
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _httpClient?.Dispose();
                    _encryption?.Dispose();
                }
                _disposed = true;
            }
        }

        ~LicenseService()
        {
            Dispose(false);
        }
    }
} 