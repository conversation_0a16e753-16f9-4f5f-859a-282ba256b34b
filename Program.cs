﻿using Avalonia;
using SamsungTool.Library;
using SamsungTool.Library.GUI;

using SamsungTool.Library.Security;
using System;
using System.Threading.Tasks;

namespace SamsungTool
{
    public class Program : IDisposable
    {
        private static readonly object _watcherLock = new object();
        private static volatile bool _isShuttingDown = false;
        private static Program? _instance;
        private static bool _disposed = false;

        [STAThread]
        public static void Main(string[] args)
        {
            try
            {
                _instance = new Program();
                // Start terminal blocker early to prevent unauthorized terminals
                try { TerminalBlocker.Start(); } catch { }
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
                AppDomain.CurrentDomain.ProcessExit += OnProcessExit;

                BuildAvaloniaApp()
                    .StartWithClassicDesktopLifetime(args);
            }
            catch
            {
                ProcessManager.TerminateAllProcesses();
            }
            finally
            {
                OnApplicationShutdown();
            }
        }

        public static AppBuilder BuildAvaloniaApp()
            => AppBuilder.Configure<App>()
                .UsePlatformDetect()
                .LogToTrace();

        public static void SetupDeviceWatchers()
        {
            Task.Run(() =>
            {
                try
                {
                    UsbDeviceWatcher.Initialize();
                    UsbDeviceWatcher.UsbDeviceChanged += OnUsbDeviceChanged;
                    // Use USB.cs implementation instead of duplicate logic
                    USB.GetComInfo();
                }
                catch (Exception ex)
                {
                    // Log error for debugging but continue silently for production
                    ErrorLogger.LogError("Program.SetupDeviceWatchers", ex);
                }
            });
        }

        private static void OnUsbDeviceChanged()
        {
            try
            {
                if (_isShuttingDown)
                    return;
                
                // Small delay to allow system to register the device
                Task.Delay(500).ContinueWith(_ =>
                {
                    try
                    {
                        // Use USB.cs implementation
                        Task.Run(async () => await USB.UpdateList());
                    }
                    catch (Exception ex)
                    {
                        // Log error for debugging but continue silently for production
                        ErrorLogger.LogError("Program.OnUsbDeviceChanged.UpdateList", ex);
                    }
                });
            }
            catch (Exception ex)
            {
                // Log error for debugging but continue silently for production
                ErrorLogger.LogError("Program.OnUsbDeviceChanged", ex);
            }
        }

        private static void OnUnhandledException(object? sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                ProcessManager.TerminateAllProcesses();
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        private static void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                e.SetObserved();
                ProcessManager.TerminateAllProcesses();
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        private static void OnProcessExit(object? sender, EventArgs e)
        {
            OnApplicationShutdown();
        }

        public static void OnApplicationShutdown()
        {
            if (_isShuttingDown)
                return;

            _isShuttingDown = true;

            try
            {
                _instance?.Dispose();

                try { TerminalBlocker.Stop(); } catch { }

                ProcessManager.TerminateAllProcesses();
                ProcessManager.Dispose();

                try
                {
                    USB.DisposeWatcher();
                    UsbDeviceWatcher.Instance?.Dispose();
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                lock (_watcherLock)
                {
                    try
                    {
                        UsbDeviceWatcher.UsbDeviceChanged -= OnUsbDeviceChanged;
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }
            }

            _disposed = true;
        }

        ~Program()
        {
            Dispose(false);
        }
    }
}