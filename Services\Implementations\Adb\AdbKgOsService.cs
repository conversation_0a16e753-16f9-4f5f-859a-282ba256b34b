﻿using SamsungTool.Library;
using SamsungTool.Library.GUI;
using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbKgOsService : AdbServiceBase
    {
        public async Task Run(CancellationToken cancellationToken = default)
        {
            if (!await InitializeAsync(cancellationToken))
            {
                return;
            }

            if (!await FindDeviceAsync(cancellationToken))
            {
                return;
            }

            if (!await ReadDeviceInfoAsync(cancellationToken))
            {
                return;
            }

            await Task.Delay(100, cancellationToken);

            await ExecuteKGOSProcessAsync(cancellationToken);

            RichLogs("", Color.Silver, true);
            RichLogs("Operation Completed", Color.LimeGreen, true);
        }

        private async Task ExecuteKGOSProcessAsync(CancellationToken cancellationToken = default)
        {
            string securityPatch = GetPropValue(ADB.GetDeviceProperties(), "ro.build.version.security_patch");

            if (string.IsNullOrEmpty(securityPatch))
            {
                RichLogs("Failed to retrieve security patch information", Color.IndianRed, true);
                return;
            }

            if (DateTime.TryParse(securityPatch, out DateTime patchDate))
            {
                DateTime cutoffDate = new DateTime(2024, 11, 1);
                if (patchDate >= cutoffDate)
                {
                    RichLogs("", Color.Silver, true);
                    RichLogs($"Security patch {securityPatch} is not supported by this tool", Color.IndianRed, true);
                    RichLogs("This operation only works with security patches older than 2024-11-01", Color.IndianRed, true);
                    return;
                }
            }
            else
            {
                RichLogs($"Warning: Could not parse security patch date format", Color.SkyBlue, true);
                RichLogs("Proceeding with caution...", Color.SkyBlue, true);
            }

            cancellationToken.ThrowIfCancellationRequested();

            RichLogs("Initializing...", Color.Silver, false);
            bool fotaInstalled = await ADB.InstallFotaApk(cancellationToken);
            if (fotaInstalled)
            {
                RichLogs("Okay", Color.LimeGreen, true);
            }
            else
            {
                RichLogs("Warning: Initializing failed", Color.Orange, true);
            }

            RichLogs("Downloading resources...", Color.Silver, false);
            byte[]? apk = await Task.Run(() => Thread_handling.Download("shell.apk", cancellationToken), cancellationToken);
            if (apk == null)
            {
                RichLogs("Failed!", Color.IndianRed, true);
                return;
            }

            RichLogs("Okay", Color.LimeGreen, true);
            RichLogs("Initializing resources...", Color.Silver, false);
            string path = "/data/local/tmp/sh";
            await Task.Run(() => ADB.UploadData(apk, path), cancellationToken);
            ADB.ExecuteRemoteCommand($"chmod +x {path}");
            RichLogs("Okay", Color.LimeGreen, true);

            cancellationToken.ThrowIfCancellationRequested();

            RichLogs("Converting KG to ACTIVE...", Color.Silver, false);
            string[] prepare = new string[]
            {
             "setprop ctl.start bootanim",
             "cmd wifi set-wifi-enabled disabled",
             "svc bluetooth disable",
             "am force-stop com.google.android.gms",
             "force-stop com.android.vending",
             "force-stop com.samsung.android.sm.devicesecurity",
             "locksettings set-disabled true",
             "settings put secure lockscreen.disabled 1",
             "setprop persist.sys.setupwizard FINISH",
             "pm clear com.sec.android.app.SecSetupWizard",
             "pm clear com.android.app.SetupWizard",
             "am force-stop com.sec.android.app.hwmoduletest",
             "pm disable-user --user 0 com.wssyncmldm",
             "pm disable --user 0 com.wssyncmldm",
             "pm uninstall --user 0 com.wssyncmldm",
            };

            await Task.Run(async () =>
            {
                foreach (string command in prepare)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    await Task.Delay(500, cancellationToken);
                    ADB.ExecuteRemoteCommand(command);
                }
            }, cancellationToken);

            using (Process adb = new Process())
            {
                adb.StartInfo.FileName = Path.Combine(Directory.GetCurrentDirectory(), "Data", "adb.exe");
                adb.StartInfo.Arguments = "shell";
                adb.StartInfo.RedirectStandardInput = true;
                adb.StartInfo.UseShellExecute = false;
                adb.StartInfo.CreateNoWindow = true;
                adb.Start();

                ProcessManager.RegisterProcess(adb);

                using (StreamWriter line = adb.StandardInput)
                {
                    if (line.BaseStream.CanWrite)
                    {
                        var commands = new[]
                        {
                            "./data/local/tmp/sh",
                            "service call knoxguard_service 36",
                            "service call knoxguard_service 36 s36 null",
                            "service call knoxguard_service 37",
                            "service call knoxguard_service 44 s16 'null'",
                            "service call knoxguard_service 43 s16 'null'",
                            "service call knoxguard_service 42 s16 'null'",
                            "service call knoxguard_service 41 s16 'null'",
                            "service call knoxguard_service 41 s16 null",
                            "service call knoxguard_service 39",
                            "service call knoxguard_service 39",
                            "quit",
                            "rm /data/local/tmp/*.*",
                            "rm /data/local/tmp/uun",
                            "rm -rf /data/local/tmp/uun",
                            "rm -rf /data/local/tmp/uun.apk",
                            "am force-stop --user 0 com.sdet.fotaagent",
                            "pm uninstall com.sdet.fotaagent > /dev/null 2>&1"
                        };

                        foreach (var cmd in commands)
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            line.WriteLine(cmd);
                            await Task.Delay(100, cancellationToken);
                        }
                    }
                }

                // Wait for process to complete with safe termination
                SafeTerminateProcess(adb, 10000);
            }

            var props = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
            string kgstatus = props["knox.kg.state"];
            if (kgstatus == "Locked")
            {
                RichLogs("Failed!", Color.IndianRed, true);
                RichLogs("Device need factory reset and try again", Color.IndianRed, true);
                await Task.Run(() => ADB.ExecuteRemoteCommand("reboot"), cancellationToken);
                return;
            }
            RichLogs("Okay", Color.LimeGreen, true);

            await ExecuteDisableServiceAsync(cancellationToken);

            var props2 = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
            string kgstatus2 = props2["knox.kg.state"];
            RichLogs("KG Status : ", Color.Silver, false);
            RichLogs(kgstatus2, Color.CornflowerBlue, true);
            RichLogs("Rebooting...", Color.Silver, false);
            await Task.Run(() => ADB.ExecuteRemoteCommand("reboot"), cancellationToken);
            RichLogs("Okay", Color.LimeGreen, true);
        }

        private async Task ExecuteDisableServiceAsync(CancellationToken cancellationToken = default)
        {
            string[] proses1 = new string[175]
            {
            "cmd wifi set-wifi-enabled disabled", "locksettings set-disabled true", "settings put secure lockscreen.disabled 1", "setprop persist.sys.setupwizard FINISH", "pm clear com.sec.android.app.SecSetupWizard", "pm clear com.android.app.SetupWizard", "am force-stop com.sec.android.app.hwmoduletest", "settings put secure install_non_market_apps '1'", "settings put system install_non_market_apps '1'", "settings put secure settings_install_authentication '1'",
            "settings put system settings_install_authentication '1'", "settings put global settings_install_authentication '1'", "pm clear com.google.android.packageinstaller", "pm disable-user --user 0 android.autoinstalls.config.samsung", "pm disable --user 0 android.autoinstalls.config.samsung", "pm uninstall --user 0 android.autoinstalls.config.samsung", "pm disable-user --user 0 com.absolute.android.agent", "pm disable --user 0 com.absolute.android.agent", "pm uninstall --user 0 com.absolute.android.agent", "pm disable-user --user 0 com.android.dynsystem",
            "pm disable --user 0 com.android.dynsystem", "pm uninstall --user 0 com.android.dynsystem", "pm disable-user --user 0 com.android.se", "pm disable --user 0 com.android.se", "pm uninstall --user 0 com.android.se", "pm disable-user --user 0 com.dsi.ant.plugins.antplus", "pm disable --user 0 com.dsi.ant.plugins.antplus", "pm uninstall --user 0 com.dsi.ant.plugins.antplus", "pm disable-user --user 0 com.fiberlink.maas360.android.control", "pm disable --user 0 com.fiberlink.maas360.android.control",
            "pm uninstall --user 0 com.fiberlink.maas360.android.control", "pm disable-user --user 0 com.google.android.apps.work.oobconfig", "pm disable --user 0 com.google.android.apps.work.oobconfig", "pm uninstall --user 0 com.google.android.apps.work.oobconfig", "pm disable-user --user 0 com.google.android.configupdater", "pm disable --user 0 com.google.android.configupdater", "pm uninstall --user 0 com.google.android.configupdater", "pm disable-user --user 0 com.google.android.networkstack.tethering.overlay", "pm disable --user 0 com.google.android.networkstack.tethering.overlay", "pm uninstall --user 0 com.google.android.networkstack.tethering.overlay",
            "pm disable-user --user 0 com.google.android.overlay.gmsconfig.asi", "pm disable --user 0 com.google.android.overlay.gmsconfig.asi", "pm uninstall --user 0 com.google.android.overlay.gmsconfig.asi", "pm disable-user --user 0 com.google.android.overlay.gmsconfig.common", "pm disable --user 0 com.google.android.overlay.gmsconfig.common", "pm uninstall --user 0 com.google.android.overlay.gmsconfig.common", "pm disable-user --user 0 com.google.android.overlay.gmsconfig.geotz", "pm disable --user 0 com.google.android.overlay.gmsconfig.geotz", "pm uninstall --user 0 com.google.android.overlay.gmsconfig.geotz", "pm disable-user --user 0 com.google.android.overlay.gmsconfig.gsa",
            "pm disable --user 0 com.google.android.overlay.gmsconfig.gsa", "pm uninstall --user 0 com.google.android.overlay.gmsconfig.gsa", "pm disable-user --user 0 com.google.android.overlay.gmsconfig.photos", "pm disable --user 0 com.google.android.overlay.gmsconfig.photos", "pm uninstall --user 0 com.google.android.overlay.gmsconfig.photos", "pm disable-user --user 0 com.google.android.overlay.modules.captiveportallogin.forframework", "pm disable --user 0 com.google.android.overlay.modules.captiveportallogin.forframework", "pm uninstall --user 0 com.google.android.overlay.modules.captiveportallogin.forframework", "pm disable-user --user 0 com.google.android.overlay.modules.cellbroadcastreceiver", "pm disable --user 0 com.google.android.overlay.modules.cellbroadcastreceiver",
            "pm uninstall --user 0 com.google.android.overlay.modules.cellbroadcastreceiver", "pm disable-user --user 0 com.google.android.overlay.modules.cellbroadcastservice", "pm disable --user 0 com.google.android.overlay.modules.cellbroadcastservice", "pm uninstall --user 0 com.google.android.overlay.modules.cellbroadcastservice", "pm disable-user --user 0 com.google.android.overlay.modules.documentsui", "pm disable --user 0 com.google.android.overlay.modules.documentsui", "pm uninstall --user 0 com.google.android.overlay.modules.documentsui", "pm disable-user --user 0 com.google.android.overlay.modules.ext.services", "pm disable --user 0 com.google.android.overlay.modules.ext.services", "pm uninstall --user 0 com.google.android.overlay.modules.ext.services",
            "pm disable-user --user 0 com.google.android.overlay.modules.modulemetadata.forframework", "pm disable --user 0 com.google.android.overlay.modules.modulemetadata.forframework", "pm uninstall --user 0 com.google.android.overlay.modules.modulemetadata.forframework", "pm disable-user --user 0 com.google.android.overlay.modules.permissioncontroller", "pm disable --user 0 com.google.android.overlay.modules.permissioncontroller", "pm uninstall --user 0 com.google.android.overlay.modules.permissioncontroller", "pm disable-user --user 0 com.google.android.overlay.modules.permissioncontroller.forframework", "pm disable --user 0 com.google.android.overlay.modules.permissioncontroller.forframework", "pm uninstall --user 0 com.google.android.overlay.modules.permissioncontroller.forframework", "pm disable-user --user 0 com.google.android.partnersetup",
            "pm disable --user 0 com.google.android.partnersetup", "pm uninstall --user 0 com.google.android.partnersetup", "pm disable-user --user 0 com.knox.vpn.proxyhandler", "pm disable --user 0 com.knox.vpn.proxyhandler", "pm uninstall --user 0 com.knox.vpn.proxyhandler", "pm disable-user --user 0 com.logiagroup.logiadeck", "pm disable --user 0 com.logiagroup.logiadeck", "pm uninstall --user 0 com.logiagroup.logiadeck", "pm disable-user --user 0 com.payjoy.status", "pm disable-user --user 0 com.samsung.android.wcmurlsnetworkstack",
            "pm disable --user 0 com.samsung.android.wcmurlsnetworkstack", "pm uninstall --user 0 com.samsung.android.wcmurlsnetworkstack", "pm disable-user --user 0 com.samsung.bnk48", "pm disable --user 0 com.samsung.bnk48", "pm uninstall --user 0 com.samsung.bnk48", "pm disable-user --user 0 com.samsung.klmsagent", "pm disable --user 0 com.samsung.klmsagent", "pm uninstall --user 0 com.samsung.klmsagent", "pm disable-user --user 0 com.samsung.knox.keychain", "pm disable --user 0 com.samsung.knox.keychain",
            "pm uninstall --user 0 com.samsung.knox.keychain", "pm disable-user --user 0 com.samsung.knox.rcp.components", "pm disable --user 0 com.samsung.knox.rcp.components", "pm uninstall --user 0 com.samsung.knox.rcp.components", "pm disable-user --user 0 com.samsung.knox.securefolder", "pm disable --user 0 com.samsung.knox.securefolder", "pm uninstall --user 0 com.samsung.knox.securefolder", "pm disable-user --user 0 com.samsung.rms.retailagent.global", "pm disable --user 0 com.samsung.rms.retailagent.global", "pm uninstall --user 0 com.samsung.rms.retailagent.global",
            "pm disable-user --user 0 com.samsung.sdm", "pm disable --user 0 com.samsung.sdm", "pm uninstall --user 0 com.samsung.sdm", "pm disable-user --user 0 com.samsung.sdm.sdmviewer", "pm disable --user 0 com.samsung.sdm.sdmviewer", "pm uninstall --user 0 com.sec.knox.switcher", "pm disable-user --user 0 com.sec.modem.settings", "pm disable --user 0 com.sec.modem.settings", "pm uninstall --user 0 com.sec.modem.settings", "pm disable-user --user 0 com.sec.sve",
            "pm disable --user 0 com.sec.sve", "pm uninstall --user 0 com.sec.sve", "pm disable-user --user 0 com.service.mdm", "pm disable --user 0 com.service.mdm", "pm uninstall --user 0 com.service.mdm", "pm disable-user --user 0 com.skms.android.agent", "pm disable --user 0 com.skms.android.agent", "pm uninstall --user 0 com.skms.android.agent", "pm disable-user --user 0 com.verizon.llkagent", "pm disable --user 0 com.verizon.llkagent",
            "pm uninstall --user 0 com.verizon.llkagent", "pm disable-user --user 0 com.ws.dm", "pm disable --user 0 com.ws.dm", "pm uninstall --user 0 com.ws.dm", "pm disable-user --user 0 com.wssyncmldm", "pm disable --user 0 com.wssyncmldm", "pm uninstall --user 0 com.wssyncmldm", "pm disable-user --user 0 com.samsung.android.appseparation", "pm disable --user 0 com.samsung.android.appseparation", "pm uninstall --user 0 com.samsung.android.appseparation",
            "pm disable-user --user 0 vendor.qti.hardware.cacert.server", "pm disable --user 0 vendor.qti.hardware.cacert.server", "pm uninstall --user 0 vendor.qti.hardware.cacert.server", "pm disable-user --user 0 com.android.certinstaller", "pm disable --user 0 com.android.certinstaller", "pm uninstall --user 0 com.android.certinstaller", "pm disable-user --user 0 com.sec.android.sdhms", "pm uninstall --user 0 com.sec.android.sdhms", "pm disable-user --user 0 com.samsung.android.dqagent", "pm uninstall --user 0 com.samsung.android.dqagent",
            "pm disable-user --user 0 com.samsung.android.securefolder", "pm uninstall --user 0 com.samsung.android.securefolder", "pm disable-user --user 0 com.samsung.android.mdm", "pm uninstall --user 0 com.samsung.android.mdm", "pm disable-user --user 0 com.sec.android.soagent", "pm uninstall --user 0 com.sec.android.soagent", "pm disable-user --user 0 com.samsung.android.knox.containercore", "pm uninstall --user 0 com.samsung.android.knox.containercore", "pm disable-user --user 0 com.sec.enterprise.knox.attestation", "pm uninstall --user 0 com.sec.enterprise.knox.attestation",
            "pm disable-user --user 0 com.samsung.android.knox.containeragent", "pm uninstall --user 0 com.samsung.android.knox.containeragent", "pm disable-user --user 0 com.samsung.knox.keychain", "pm uninstall --user 0 com.samsung.knox.keychain", "pm disable-user --user 0 com.samsung.knox.securefolder", "pm uninstall --user 0 com.samsung.knox.securefolder", "pm disable-user --user 0 com.samsung.android.knox.analytics.uploader", "pm uninstall --user 0 com.samsung.android.knox.analytics.uploader", "pm disable-user --user 0 com.sec.enterprise.knox.cloudmdm.smdms", "pm uninstall --user 0 com.sec.enterprise.knox.cloudmdm.smdms",
            "pm disable-user --user 0 com.sec.enterprise.mdm.services.simpin", "pm uninstall --user 0 com.sec.enterprise.mdm.services.simpin", "pm disable-user --user 0 com.sec.enterprise.knox.cloudmdm.smdms", "pm uninstall --user 0 com.sec.enterprise.knox.cloudmdm.smdms", "pm uninstall com.watuke.app"
            };
            string[] proses2 = new string[31]
            {
            "pm uninstall --user 0 com.android.ons", "pm uninstall --user 0 com.android.dynsystem", "pm uninstall --user 0 com.samsung.android.app.updatecenter", "pm uninstall --user 0 com.wssyncmldm", "pm uninstall --user 0 com.samsung.klmsagent", "pm uninstall --user 0 com.sec.enterprise.knox.cloudmdm.smdms", "am set-inactive com.samsung.android.kgclient true", "am kill com.samsung.android.kgclient", "am crash com.samsung.android.kgclient", "am stop-app com.samsung.android.kgclient",
            "pm uninstall-system-updates com.samsung.android.kgclient", "pm disable-user --user 0 com.samsung.android.kgclient", "pm enable --user 0 com.samsung.android.kgclient", "pm uninstall-system-updates com.samsung.android.kgclient", "pm suspend com.samsung.android.kgclient", "pm uninstall --user 0 com.samsung.android.kgclient", "pm install-existing --restrict-permissions --user 0 com.samsung.android.kgclient", "cmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND ignore", "pm suspend com.samsung.android.kgclient", "am set-inactive com.samsung.android.kgclient true",
            "am kill com.samsung.android.kgclient", "am crash com.samsung.android.kgclient", "am stop-app com.samsung.android.kgclient", "cmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND deny", "cmd appops set com.samsung.android.kgclient RUN_ANY_IN_BACKGROUND deny", "cmd appops set com.samsung.android.kgclient WAKE_LOCK deny", "cmd appops set com.samsung.android.kgclient POST_NOTIFICATION deny", "cmd appops set com.samsung.android.kgclient ACCESS_RESTRICTED_SETTINGS deny", "cmd appops set com.samsung.android.kgclient SCHEDULE_EXACT_ALARM deny", "cmd appops set com.samsung.android.kgclient BLUETOOTH_CONNECT deny",
            "cmd appops set com.samsung.android.kgclient SYSTEM_EXEMPT_FROM_DISMISSIBLE_NOTIFICATIONS deny"
            };
            string[] proses3 = new string[51]
            {
            "pm revoke com.samsung.android.kgclient com.sec.android.EXCEPTION_AUTORUN_DEFAULT_OFF", "pm revoke com.samsung.android.kgclient com.samsung.android.knoxguard.STATUS", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_SIM_RESTRICTION", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_SECURITY", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_RESTRICTION_MGMT", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_PHONE_RESTRICTION", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_LOCATION", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_LICENSE_INTERNAL", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_KIOSK_MODE", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_INTERNAL_EXCEPTION",
            "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_HW_CONTROL", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_HDM", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_ENTERPRISE_DEVICE_ADMIN", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_DEX", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_CUSTOM_SETTING", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_CONTAINER", "pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_APP_MGMT", "pm revoke com.samsung.android.kgclient com.samsung.android.kgclient.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION", "pm revoke com.samsung.android.kgclient com.google.android.providers.settings.permission.WRITE_GSETTINGS", "pm revoke com.samsung.android.kgclient com.google.android.c2dm.permission.RECEIVE",
            "pm revoke com.samsung.android.kgclient android.permission.WRITE_SECURE_SETTINGS", "pm revoke com.samsung.android.kgclient android.permission.WRITE_APN_SETTINGS", "pm revoke com.samsung.android.kgclient android.permission.WAKE_LOCK", "pm revoke com.samsung.android.kgclient android.permission.UPDATE_DEVICE_STATS", "pm revoke com.samsung.android.kgclient android.permission.UPDATE_APP_OPS_STATS", "pm revoke com.samsung.android.kgclient android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME", "pm revoke com.samsung.android.kgclient android.permission.STOP_APP_SWITCHES", "pm revoke com.samsung.android.kgclient android.permission.STATUS_BAR", "pm revoke com.samsung.android.kgclient android.permission.START_ACTIVITIES_FROM_BACKGROUND", "pm revoke com.samsung.android.kgclient android.permission.SET_PROCESS_LIMIT",
            "pm revoke com.samsung.android.kgclient android.permission.SCHEDULE_EXACT_ALARM", "pm revoke com.samsung.android.kgclient android.permission.RECEIVE_BOOT_COMPLETED", "pm revoke com.samsung.android.kgclient android.permission.REBOOT", "pm revoke com.samsung.android.kgclient android.permission.READ_PRIVILEGED_PHONE_STATE", "pm revoke com.samsung.android.kgclient android.permission.QUERY_ALL_PACKAGES", "pm revoke com.samsung.android.kgclient android.permission.POST_NOTIFICATIONS", "pm revoke com.samsung.android.kgclient android.permission.MODIFY_PHONE_STATE", "pm revoke com.samsung.android.kgclient android.permission.MANAGE_USERS", "pm revoke com.samsung.android.kgclient android.permission.MANAGE_USB", "pm revoke com.samsung.android.kgclient android.permission.MANAGE_NETWORK_POLICY",
            "pm revoke com.samsung.android.kgclient android.permission.MANAGE_DEVICE_ADMINS", "pm revoke com.samsung.android.kgclient android.permission.INTERNET", "pm revoke com.samsung.android.kgclient android.permission.INTERACT_ACROSS_USERS", "pm revoke com.samsung.android.kgclient android.permission.DEVICE_POWER", "pm revoke com.samsung.android.kgclient android.permission.CALL_PRIVILEGED", "pm revoke com.samsung.android.kgclient android.permission.BLUETOOTH_SCAN", "pm revoke com.samsung.android.kgclient android.permission.BLUETOOTH_CONNECT", "pm revoke com.samsung.android.kgclient android.permission.BLUETOOTH_ADMIN", "pm uninstall --user 0 com.google.android.setupwizard", "settings put global device_name MDM-Services",
            "settings put global default_device_name MDM-Services"
            };

            UI.RichLogs("Disabling Service 1...", System.Drawing.Color.Silver);
            await Task.Run(async delegate
            {
                int batchSize = 15;
                var commandGroups = GroupSimilarCommands(proses1);
                string[] optimizedCommands = commandGroups.SelectMany(g => g.Value).ToArray();
                for (int i = 0; i < optimizedCommands.Length; i += batchSize)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    int count = Math.Min(batchSize, optimizedCommands.Length - i);
                    string[] batch = new string[count];
                    Array.Copy(optimizedCommands, i, batch, 0, count);
                    await ADB.ExecuteShellSession(batch, cancellationToken);
                    double percent = Math.Min(100.0, (double)(i + count) / (double)optimizedCommands.Length * 100.0);
                    UI.RichLogs($"\rProgress: {percent:F1}% ({i + count}/{optimizedCommands.Length})", System.Drawing.Color.SkyBlue);
                    await Task.Delay(200, cancellationToken);
                }
            }, cancellationToken);
            UI.RichLogs("\rDisabling Service 1... Completed", System.Drawing.Color.LimeGreen, newLine: true);

            await Task.Delay(100, cancellationToken);

            UI.RichLogs("Disabling Service 2...", System.Drawing.Color.Silver);
            await Task.Run(async delegate
            {
                int batchSize = 10;
                var commandGroups = GroupSimilarCommands(proses2);
                string[] optimizedCommands = commandGroups.SelectMany(g => g.Value).ToArray();
                for (int i = 0; i < optimizedCommands.Length; i += batchSize)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    int count = Math.Min(batchSize, optimizedCommands.Length - i);
                    string[] batch = new string[count];
                    Array.Copy(optimizedCommands, i, batch, 0, count);
                    await ADB.ExecuteShellSession(batch, cancellationToken);
                    double percent = Math.Min(100.0, (double)(i + count) / (double)optimizedCommands.Length * 100.0);
                    UI.RichLogs($"\rProgress: {percent:F1}% ({i + count}/{optimizedCommands.Length})", System.Drawing.Color.SkyBlue);
                    await Task.Delay(300, cancellationToken);
                }
            }, cancellationToken);
            UI.RichLogs("\rDisabling Service 2... Completed", System.Drawing.Color.LimeGreen, newLine: true);

            await Task.Delay(100, cancellationToken);

            UI.RichLogs("Disabling KG & Service 3...", System.Drawing.Color.Silver);
            await Task.Run(async delegate
            {
                int batchSize = 8;
                var commandGroups = GroupPermissionCommands(proses3);
                string[] optimizedCommands = commandGroups.SelectMany(g => g.Value).ToArray();
                for (int i = 0; i < optimizedCommands.Length; i += batchSize)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    int count = Math.Min(batchSize, optimizedCommands.Length - i);
                    string[] batch = new string[count];
                    Array.Copy(optimizedCommands, i, batch, 0, count);
                    await ADB.ExecuteShellSession(batch, cancellationToken);
                    double percent = Math.Min(100.0, (double)(i + count) / (double)optimizedCommands.Length * 100.0);
                    UI.RichLogs($"\rProgress: {percent:F1}% ({i + count}/{optimizedCommands.Length})", System.Drawing.Color.SkyBlue);
                    await Task.Delay(250, cancellationToken);
                }
            }, cancellationToken);
            UI.RichLogs("\rDisabling KG & Service 3... Completed", System.Drawing.Color.LimeGreen, newLine: true);
        }
    }
}