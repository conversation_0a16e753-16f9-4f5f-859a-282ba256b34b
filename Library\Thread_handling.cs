﻿using SamsungTool.Library;
using SamsungTool.Services.Interfaces;
using SamsungTool.Services.Implementations;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Library
{
    public class Thread_handling : IDisposable
    {
        private readonly IAdbService _adbService;
        private readonly IMtpService _mtpService;
        private readonly IDownloadModeService _downloadModeService;
        private readonly IFlashService _flashService;
        private readonly IDownloadService _downloadService;
        private bool _disposed = false;

        public static long flashFlashedSize;
        public static long flashTotalSize;

        public class ObjectFirmwares
        {
            public string? name { get; set; }
            public long size { get; set; }
        }

        public static List<ObjectFirmwares> firmwareinfo = new List<ObjectFirmwares>();

        public Thread_handling()
        {
            _adbService = new AdbService();
            _mtpService = new MtpService();
            _downloadModeService = new DownloadModeService();
            _flashService = new FlashService();
            _downloadService = new DownloadService();
        }

        public Thread_handling(IAdbService adbService, IMtpService mtpService, IDownloadModeService downloadModeService, IFlashService flashService, IDownloadService downloadService)
        {
            _adbService = adbService ?? throw new ArgumentNullException(nameof(adbService));
            _mtpService = mtpService ?? throw new ArgumentNullException(nameof(mtpService));
            _downloadModeService = downloadModeService ?? throw new ArgumentNullException(nameof(downloadModeService));
            _flashService = flashService ?? throw new ArgumentNullException(nameof(flashService));
            _downloadService = downloadService ?? throw new ArgumentNullException(nameof(downloadService));
        }

        public static async Task<byte[]?> Download(string filename, CancellationToken cancellationToken = default)
        {
            var downloadService = new DownloadService();
            return await downloadService.DownloadAsync(filename, cancellationToken);
        }

        public static async Task StartFlashAsync(IProgress<ProgressReport> progress, CancellationToken cancellationToken = default)
        {
            var flashService = new FlashService();
            await flashService.StartFlashAsync(progress, cancellationToken);
        }

        public static async Task MTPThread(string Operation, int? os = null, CancellationToken cancellationToken = default)
        {
            var mtpService = new MtpService();
            await mtpService.ExecuteOperationAsync(Operation, os, cancellationToken);
        }

        public static async Task ADBThread(string Operation, CancellationToken cancellationToken = default)
        {
            var adbService = new AdbService();
            await adbService.ExecuteOperationAsync(Operation, cancellationToken);
        }

        public static async Task KgRemove2025(CancellationToken cancellationToken = default)
        {
            var adbService = new AdbService();
            await adbService.KgRemove2025Async(cancellationToken);
        }

        public static async Task ExecuteServerBinaryOperation(string operationName, string jobName, bool requiresInitialSetup = true, CancellationToken cancellationToken = default)
        {
            var adbService = new AdbService();
            await adbService.ExecuteServerBinaryOperationAsync(operationName, jobName, requiresInitialSetup, cancellationToken);
        }

        public static async Task ExecuteRawShellCommands(string commandsText, CancellationToken cancellationToken = default)
        {
            var adbService = new AdbService();
            await adbService.ExecuteRawShellCommandsAsync(commandsText, cancellationToken);
        }

        public static async Task DisableService(CancellationToken cancellationToken = default)
        {
            var adbService = new AdbService();
            await adbService.DisableServiceAsync(cancellationToken);
        }

        public static async Task DownloadmodeThread(string Operation, CancellationToken cancellationToken = default)
        {
            var downloadModeService = new DownloadModeService();
            await downloadModeService.ExecuteOperationAsync(Operation, cancellationToken);
        }

        public static string? ReadInfoOdin(string port)
        {
            var downloadModeService = new DownloadModeService();
            return downloadModeService.ReadInfoOdin(port);
        }

        public static async Task ReadinfoMTKFRP()
        {
            var downloadModeService = new DownloadModeService();
            await downloadModeService.ReadInfoMTKFRPAsync();
        }

        public async Task MTPThreadAsync(string Operation, int? os = null, CancellationToken cancellationToken = default)
        {
            await _mtpService.ExecuteOperationAsync(Operation, os, cancellationToken);
        }

        public async Task ADBThreadAsync(string Operation, CancellationToken cancellationToken = default)
        {
            await _adbService.ExecuteOperationAsync(Operation, cancellationToken);
        }

        public async Task KgRemove2025Async(CancellationToken cancellationToken = default)
        {
            await _adbService.KgRemove2025Async(cancellationToken);
        }

        public async Task ExecuteServerBinaryOperationAsync(string operationName, string jobName, bool requiresInitialSetup = true, CancellationToken cancellationToken = default)
        {
            await _adbService.ExecuteServerBinaryOperationAsync(operationName, jobName, requiresInitialSetup, cancellationToken);
        }

        public async Task ExecuteRawShellCommandsAsync(string commandsText, CancellationToken cancellationToken = default)
        {
            await _adbService.ExecuteRawShellCommandsAsync(commandsText, cancellationToken);
        }

        public async Task DisableServiceAsync(CancellationToken cancellationToken = default)
        {
            await _adbService.DisableServiceAsync(cancellationToken);
        }

        public async Task DownloadmodeThreadAsync(string Operation, CancellationToken cancellationToken = default)
        {
            await _downloadModeService.ExecuteOperationAsync(Operation, cancellationToken);
        }

        public string? ReadInfoOdinInstance(string port)
        {
            return _downloadModeService.ReadInfoOdin(port);
        }

        public async Task ReadinfoMTKFRPAsync()
        {
            await _downloadModeService.ReadInfoMTKFRPAsync();
        }

        public async Task StartFlashInstanceAsync(IProgress<ProgressReport> progress, CancellationToken cancellationToken = default)
        {
            await _flashService.StartFlashAsync(progress, cancellationToken);
        }

        public async Task<byte[]?> DownloadAsync(string filename, CancellationToken cancellationToken = default)
        {
            return await _downloadService.DownloadAsync(filename, cancellationToken);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                try
                {
                    if (_adbService is IDisposable disposableAdb)
                        disposableAdb.Dispose();
                }
                catch (Exception)
                {
                    // Suppress exceptions during disposal
                }

                try
                {
                    if (_mtpService is IDisposable disposableMtp)
                        disposableMtp.Dispose();
                }
                catch (Exception)
                {
                    // Suppress exceptions during disposal
                }

                try
                {
                    if (_downloadModeService is IDisposable disposableDownloadMode)
                        disposableDownloadMode.Dispose();
                }
                catch (Exception)
                {
                    // Suppress exceptions during disposal
                }

                try
                {
                    if (_flashService is IDisposable disposableFlash)
                        disposableFlash.Dispose();
                }
                catch (Exception)
                {
                    // Suppress exceptions during disposal
                }

                try
                {
                    if (_downloadService is IDisposable disposableDownload)
                        disposableDownload.Dispose();
                }
                catch (Exception)
                {
                    // Suppress exceptions during disposal
                }
            }

            _disposed = true;
        }

        ~Thread_handling()
        {
            Dispose(false);
        }
    }
}