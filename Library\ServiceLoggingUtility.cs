using System;
using System.Drawing;
using SamsungTool.Library.GUI;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Library
{
    /// <summary>
    /// Centralized logging utility for services to reduce code duplication
    /// Provides consistent logging patterns across all services
    /// </summary>
    public static class ServiceLoggingUtility
    {
        // Standard colors used across services
        public static readonly Color InfoColor = Color.Silver;
        public static readonly Color SuccessColor = Color.LimeGreen;
        public static readonly Color ErrorColor = Color.IndianRed;
        public static readonly Color WarningColor = Color.Orange;
        public static readonly Color HighlightColor = Color.CornflowerBlue;
        public static readonly Color ProcessColor = Color.Goldenrod;

        /// <summary>
        /// Logs operation start with standard formatting
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="clearLog">Whether to clear the log first</param>
        public static void LogOperationStart(string operationName, bool clearLog = false)
        {
            if (clearLog)
            {
                UI.ClearLog();
            }
            RichLogs($"Operation: {operationName}", InfoColor, true);
        }

        /// <summary>
        /// Logs operation completion with standard formatting
        /// </summary>
        /// <param name="customMessage">Custom completion message (default: "Operation Completed")</param>
        public static void LogOperationCompleted(string customMessage = "Operation Completed")
        {
            RichLogs("", InfoColor, true);
            RichLogs(customMessage, SuccessColor, true);
        }

        /// <summary>
        /// Logs a step in progress
        /// </summary>
        /// <param name="message">Step message</param>
        /// <param name="showInProgress">Whether to show as in-progress (no newline)</param>
        public static void LogStep(string message, bool showInProgress = true)
        {
            RichLogs(message, InfoColor, !showInProgress);
        }

        /// <summary>
        /// Logs step completion (typically "Okay")
        /// </summary>
        /// <param name="message">Completion message (default: "Okay")</param>
        public static void LogStepCompleted(string message = "Okay")
        {
            RichLogs(message, SuccessColor, true);
        }

        /// <summary>
        /// Logs step failure (typically "Failed!")
        /// </summary>
        /// <param name="message">Failure message (default: "Failed!")</param>
        public static void LogStepFailed(string message = "Failed!")
        {
            RichLogs(message, ErrorColor, true);
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">Error message</param>
        public static void LogError(string message)
        {
            RichLogs(message, ErrorColor, true);
        }

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">Warning message</param>
        public static void LogWarning(string message)
        {
            RichLogs(message, WarningColor, true);
        }

        /// <summary>
        /// Logs an informational message
        /// </summary>
        /// <param name="message">Information message</param>
        public static void LogInfo(string message)
        {
            RichLogs(message, InfoColor, true);
        }

        /// <summary>
        /// Logs highlighted information (typically device info, serial numbers, etc.)
        /// </summary>
        /// <param name="message">Message to highlight</param>
        public static void LogHighlight(string message)
        {
            RichLogs(message, HighlightColor, true);
        }

        /// <summary>
        /// Logs process-related information
        /// </summary>
        /// <param name="message">Process message</param>
        public static void LogProcess(string message)
        {
            RichLogs(message, ProcessColor, true);
        }

        /// <summary>
        /// Logs a blank line for spacing
        /// </summary>
        public static void LogBlankLine()
        {
            RichLogs("", InfoColor, true);
        }

        /// <summary>
        /// Logs initialization status
        /// </summary>
        /// <param name="message">Initialization message</param>
        /// <param name="success">Whether initialization was successful</param>
        public static void LogInitialization(string message, bool success)
        {
            if (success)
            {
                LogStep(message);
                LogStepCompleted();
            }
            else
            {
                LogStep(message);
                LogStepFailed();
            }
        }

        /// <summary>
        /// Logs device connection status
        /// </summary>
        /// <param name="deviceInfo">Device information to display</param>
        public static void LogDeviceFound(string deviceInfo)
        {
            LogHighlight(deviceInfo);
        }

        /// <summary>
        /// Logs waiting for device
        /// </summary>
        public static void LogWaitingForDevice()
        {
            LogStep("Waiting for device...");
        }

        /// <summary>
        /// Logs device connection
        /// </summary>
        /// <param name="portName">Port name</param>
        public static void LogDeviceConnection(string portName)
        {
            LogHighlight(portName);
            LogStep($"Connecting to {portName}...");
            LogStepCompleted();
        }

        /// <summary>
        /// Logs operation cancellation
        /// </summary>
        /// <param name="operationName">Name of the cancelled operation</param>
        public static void LogOperationCancelled(string operationName = "Operation")
        {
            LogWarning($"{operationName} was cancelled");
        }

        /// <summary>
        /// Logs exception with standard formatting
        /// </summary>
        /// <param name="context">Context where exception occurred</param>
        /// <param name="ex">Exception to log</param>
        /// <param name="logToErrorLogger">Whether to also log to ErrorLogger for debugging</param>
        public static void LogException(string context, Exception ex, bool logToErrorLogger = true)
        {
            LogError($"Error in {context}: {ex.Message}");
            
            if (logToErrorLogger)
            {
                ErrorLogger.LogError(context, ex);
            }
        }

        /// <summary>
        /// Logs progress with percentage
        /// </summary>
        /// <param name="message">Progress message</param>
        /// <param name="percentage">Progress percentage (0-100)</param>
        public static void LogProgress(string message, int percentage)
        {
            LogInfo($"{message} ({percentage}%)");
        }

        /// <summary>
        /// Logs a separator line for visual organization
        /// </summary>
        /// <param name="character">Character to use for separator (default: "-")</param>
        /// <param name="length">Length of separator (default: 50)</param>
        public static void LogSeparator(char character = '-', int length = 50)
        {
            LogInfo(new string(character, length));
        }

        /// <summary>
        /// Logs device information in a structured format
        /// </summary>
        /// <param name="serialNumber">Device serial number</param>
        /// <param name="model">Device model</param>
        /// <param name="additionalInfo">Additional device information</param>
        public static void LogDeviceInfo(string? serialNumber, string? model = null, string? additionalInfo = null)
        {
            if (!string.IsNullOrEmpty(serialNumber))
            {
                LogHighlight($"SN: {serialNumber.ToUpper()}");
            }
            
            if (!string.IsNullOrEmpty(model))
            {
                LogHighlight($"Model: {model}");
            }
            
            if (!string.IsNullOrEmpty(additionalInfo))
            {
                LogInfo(additionalInfo);
            }
        }

        /// <summary>
        /// Logs retry attempt
        /// </summary>
        /// <param name="attempt">Current attempt number</param>
        /// <param name="maxAttempts">Maximum number of attempts</param>
        /// <param name="operation">Operation being retried</param>
        public static void LogRetryAttempt(int attempt, int maxAttempts, string operation)
        {
            LogWarning($"Retrying {operation} (attempt {attempt}/{maxAttempts})...");
        }
    }
}
