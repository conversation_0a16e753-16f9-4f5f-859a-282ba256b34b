﻿<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="SamsungTool.LoginWindow"
        Title="Authentication Required"
        Width="450" Height="630"
        WindowStartupLocation="CenterScreen"
        Background="#1E1E1E"
        TransparencyLevelHint="AcrylicBlur"
        ExtendClientAreaToDecorationsHint="True"
        ExtendClientAreaTitleBarHeightHint="-1"
        CanResize="False"
        Icon="avares://SamsungTool/Resources/logo.ico">

	<Window.Styles>
		<!-- Header Animation Styles -->
		<Style Selector="TextBlock.header">
			<Setter Property="FontSize" Value="26"/>
			<Setter Property="FontWeight" Value="Bold"/>
			<Setter Property="Foreground" Value="#CC2E2E"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateY(-20px)"/>
		</Style>

		<Style Selector="TextBlock.header.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateY(0px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.8" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.8" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBlock.subtitle">
			<Setter Property="FontSize" Value="14"/>
			<Setter Property="Foreground" Value="#CCCCCC"/>
			<Setter Property="Margin" Value="0 8 0 0"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateY(-15px)"/>
		</Style>

		<Style Selector="TextBlock.subtitle.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateY(0px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:1.0" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:1.0" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBlock.section-header">
			<Setter Property="FontSize" Value="16"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="Foreground" Value="#CC2E2E"/>
			<Setter Property="Margin" Value="0 0 0 15"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(-15px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.6" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.6" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBlock.section-header.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateX(0px)"/>
		</Style>

		<!-- Enhanced TextBox Styles with Animations -->
		<Style Selector="TextBox">
			<Setter Property="Background" Value="#2D2D30"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="Padding" Value="12 8"/>
			<Setter Property="Height" Value="38"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="BorderBrush" Value="#404040"/>
			<Setter Property="CornerRadius" Value="4"/>
			<Setter Property="Margin" Value="0 0 0 12"/>
			<Setter Property="FontSize" Value="14"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(-10px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.6" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.6" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBox.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateX(0px)"/>
		</Style>

		<Style Selector="TextBox:focus /template/ Border#PART_BorderElement">
			<Setter Property="Background" Value="#3A3A3D"/>
			<Setter Property="BorderBrush" Value="#007ACC"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Background" Duration="0:0:0.2"/>
					<BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBox:pointerover /template/ Border#PART_BorderElement">
			<Setter Property="Background" Value="#353538"/>
			<Setter Property="BorderBrush" Value="#606060"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Background" Duration="0:0:0.15"/>
					<BrushTransition Property="BorderBrush" Duration="0:0:0.15"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBox.hwid-textbox">
			<Setter Property="Background" Value="#1A1A1C"/>
			<Setter Property="Foreground" Value="#DDDDDD"/>
			<Setter Property="Padding" Value="8 6"/>
			<Setter Property="Height" Value="32"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="BorderBrush" Value="#404040"/>
			<Setter Property="CornerRadius" Value="4"/>
			<Setter Property="Margin" Value="0"/>
			<Setter Property="FontSize" Value="11"/>
			<Setter Property="FontFamily" Value="Consolas,monospace"/>
			<Setter Property="IsReadOnly" Value="True"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
		</Style>

		<!-- Enhanced CheckBox with Animations -->
		<Style Selector="CheckBox">
			<Setter Property="Foreground" Value="#CCCCCC"/>
			<Setter Property="Margin" Value="0 0 0 12"/>
			<Setter Property="FontSize" Value="13"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(-10px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.6" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.6" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="CheckBox.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateX(0px)"/>
		</Style>

		<!-- Enhanced Form Labels with Animations -->
		<Style Selector="TextBlock.form-label">
			<Setter Property="Foreground" Value="#DDDDDD"/>
			<Setter Property="VerticalAlignment" Value="Center"/>
			<Setter Property="Margin" Value="0 0 0 6"/>
			<Setter Property="FontWeight" Value="Medium"/>
			<Setter Property="FontSize" Value="13"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(-8px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.5" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.5" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBlock.form-label.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateX(0px)"/>
		</Style>

		<!-- Enhanced Footer Text with Animations -->
		<Style Selector="TextBlock.footer-text">
			<Setter Property="Foreground" Value="#AAAAAA"/>
			<Setter Property="FontSize" Value="12"/>
			<Setter Property="TextAlignment" Value="Center"/>
			<Setter Property="FontStyle" Value="Italic"/>
			<Setter Property="VerticalAlignment" Value="Center"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateY(10px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.8" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.8" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBlock.footer-text.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateY(0px)"/>
		</Style>

		<!-- Enhanced Website Button with Animations -->
		<Style Selector="Button#WebsiteButton">
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="scale(0.8) translateY(10px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.8" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.8" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Button#WebsiteButton.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="scale(1.0) translateY(0px)"/>
		</Style>

		<Style Selector="Button#WebsiteButton:pointerover">
			<Setter Property="RenderTransform" Value="scale(1.1) translateY(0px)"/>
		</Style>

		<Style Selector="Button#WebsiteButton:pressed">
			<Setter Property="RenderTransform" Value="scale(0.95) translateY(0px)"/>
		</Style>

		<Style Selector="TextBlock.status-text">
			<Setter Property="Foreground" Value="#CCCCCC"/>
			<Setter Property="FontStyle" Value="Italic"/>
			<Setter Property="TextAlignment" Value="Center"/>
			<Setter Property="HorizontalAlignment" Value="Center"/>
			<Setter Property="VerticalAlignment" Value="Center"/>
			<Setter Property="Height" Value="20"/>
			<Setter Property="FontSize" Value="13"/>
		</Style>

		<!-- Enhanced Copy Button with Animations -->
		<Style Selector="Button.copy-button">
			<Setter Property="Background" Value="#444455"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="BorderBrush" Value="#555566"/>
			<Setter Property="CornerRadius" Value="4"/>
			<Setter Property="Padding" Value="8 4"/>
			<Setter Property="Height" Value="32"/>
			<Setter Property="FontWeight" Value="Medium"/>
			<Setter Property="FontSize" Value="11"/>
			<Setter Property="VerticalAlignment" Value="Center"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="HorizontalContentAlignment" Value="Center"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Background" Duration="0:0:0.2"/>
					<BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.1"/>
				</Transitions>
			</Setter>
		</Style>
		<Style Selector="Button.copy-button:pointerover">
			<Setter Property="Background" Value="#555566"/>
			<Setter Property="BorderBrush" Value="#666677"/>
			<Setter Property="RenderTransform" Value="scale(1.05)"/>
		</Style>
		<Style Selector="Button.copy-button:pressed">
			<Setter Property="RenderTransform" Value="scale(0.95)"/>
		</Style>

		<!-- Enhanced Eye Button with Animations -->
		<Style Selector="Button.eye-button">
			<Setter Property="Background" Value="Transparent"/>
			<Setter Property="BorderThickness" Value="0"/>
			<Setter Property="Padding" Value="5"/>
			<Setter Property="Cursor" Value="Hand"/>
			<Setter Property="ZIndex" Value="5"/>
			<Setter Property="CornerRadius" Value="3"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="HorizontalContentAlignment" Value="Center"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Background" Duration="0:0:0.2"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.1"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Button.eye-button:pointerover">
			<Setter Property="Background" Value="#FFFFFF20"/>
			<Setter Property="RenderTransform" Value="scale(1.1)"/>
		</Style>

		<Style Selector="Button.eye-button:pressed">
			<Setter Property="RenderTransform" Value="scale(0.9)"/>
		</Style>

		<!-- Enhanced Login Button with Animations -->
		<Style Selector="Button.login-button">
			<Setter Property="Background" Value="#007ACC"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="BorderBrush" Value="#0099DD"/>
			<Setter Property="CornerRadius" Value="4"/>
			<Setter Property="Padding" Value="0 12"/>
			<Setter Property="Height" Value="42"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="FontSize" Value="14"/>
			<Setter Property="HorizontalAlignment" Value="Stretch"/>
			<Setter Property="HorizontalContentAlignment" Value="Center"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="Margin" Value="0 15 0 0"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="scale(0.95) translateY(10px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.8" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.8" Easing="CubicEaseOut"/>
					<BrushTransition Property="Background" Duration="0:0:0.2"/>
					<BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Button.login-button.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="scale(1.0) translateY(0px)"/>
		</Style>

		<Style Selector="Button.login-button:pointerover">
			<Setter Property="Background" Value="#0091EA"/>
			<Setter Property="BorderBrush" Value="#00CCFF"/>
			<Setter Property="RenderTransform" Value="scale(1.02) translateY(0px)"/>
		</Style>

		<Style Selector="Button.login-button:pressed">
			<Setter Property="RenderTransform" Value="scale(0.98) translateY(0px)"/>
		</Style>

		<!-- Enhanced Main Container with Animations -->
		<Style Selector="Border.main-container">
			<Setter Property="Background" Value="#252526"/>
			<Setter Property="CornerRadius" Value="8"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="BorderBrush" Value="#404040"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="scale(0.9) translateY(20px)"/>
			<Setter Property="BoxShadow" Value="0 10 30 0 #40000000"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:1.0" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:1.0" Easing="CubicEaseOut"/>
					<BoxShadowsTransition Property="BoxShadow" Duration="0:0:0.3"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Border.main-container.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="scale(1.0) translateY(0px)"/>
		</Style>

		<Style Selector="Border.main-container:pointerover">
			<Setter Property="BoxShadow" Value="0 15 40 0 #50000000"/>
		</Style>

		<Style Selector="Border.update-overlay">
			<Setter Property="Background" Value="#CC000000"/>
			<Setter Property="CornerRadius" Value="8"/>
			<Setter Property="BorderThickness" Value="2"/>
			<Setter Property="BorderBrush" Value="#007ACC"/>
		</Style>

		<Style Selector="TextBlock.update-title">
			<Setter Property="FontSize" Value="18"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="Foreground" Value="#FFFFFF"/>
			<Setter Property="TextAlignment" Value="Center"/>
			<Setter Property="Margin" Value="0 0 0 10"/>
		</Style>

		<Style Selector="ProgressBar.update-progress">
			<Setter Property="Height" Value="8"/>
			<Setter Property="Background" Value="#404040"/>
			<Setter Property="Foreground" Value="#007ACC"/>
			<Setter Property="CornerRadius" Value="4"/>
			<Setter Property="Margin" Value="0 10 0 0"/>
		</Style>

		<Style Selector="Button">
			<Setter Property="HorizontalContentAlignment" Value="Center"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="Cursor" Value="Hand"/>
		</Style>
	</Window.Styles>

	<Grid Name="MainGrid" Opacity="0" RenderTransform="scale(0.95)">
		<Grid.Transitions>
			<Transitions>
				<DoubleTransition Property="Opacity" Duration="0:0:0.5" Easing="CubicEaseOut"/>
				<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.5" Easing="CubicEaseOut"/>
			</Transitions>
		</Grid.Transitions>

		<Grid RowDefinitions="Auto,*,Auto">
			<StackPanel Grid.Row="0" Margin="0,30,0,20" Name="HeaderPanel">
				<TextBlock Text="SAMSUNG TOOL" Classes="header" HorizontalAlignment="Center" Name="HeaderText"/>
				<TextBlock Text="Please sign in to continue" Classes="subtitle" HorizontalAlignment="Center" Name="SubtitleText"/>
			</StackPanel>

			<Border Grid.Row="1" Classes="main-container" Margin="30,0" Padding="20,18" Name="MainContainer">
				<StackPanel Name="FormPanel">
					<TextBlock Text="AUTHENTICATION" Classes="section-header" Name="SectionHeader"/>

					<TextBlock Text="Hardware ID:" Classes="form-label" Name="HwidLabel"/>
					<Grid ColumnDefinitions="*,Auto" Margin="0,0,0,12" Name="HwidGrid">
						<TextBox Grid.Column="0" Name="HWIDTextBox" Classes="hwid-textbox"
								 Text="Generating..." IsReadOnly="True"
								 Watermark="Hardware ID will appear here"/>
						<Button Grid.Column="1" Name="CopyHWIDButton" Classes="copy-button"
								Content="COPY" ToolTip.Tip="Copy HWID to clipboard"
								Margin="8,0,0,0"/>
					</Grid>

					<TextBlock Text="Username:" Classes="form-label" Margin="0,5,0,6" Name="UsernameLabel"/>
					<TextBox Name="UsernameTextBox" Watermark="Enter your username"/>

					<TextBlock Text="Password:" Classes="form-label" Margin="0,5,0,6" Name="PasswordLabel"/>
					<Grid Name="PasswordGrid">
						<TextBox Name="PasswordTextBox" PasswordChar="•"
                                 Watermark="Enter your password" Padding="12 8 40 8"/>
						<Button Name="ShowPasswordButton" Classes="eye-button"
                                ToolTip.Tip="Show/Hide Password"
                                HorizontalAlignment="Right" VerticalAlignment="Center"
                                Margin="0,0,6,10" Width="26" Height="26">
							<Panel>
								<Viewbox Width="16" Height="16">
									<Canvas Width="24" Height="24">
										<Path Fill="#BBBBBB" Data="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
									</Canvas>
								</Viewbox>
							</Panel>
						</Button>
					</Grid>

					<CheckBox Name="RememberMeCheckBox" Content="Remember Me" IsChecked="True" Margin="0,8,0,0"/>

					<Button Name="LoginButton" Content="LOGIN" Classes="login-button"/>
				</StackPanel>
			</Border>

			<StackPanel Grid.Row="2" Margin="0,20,0,25" VerticalAlignment="Center" Name="FooterPanel">
				<Grid Height="20" Margin="0,0,0,10">
					<TextBlock Name="StatusLabel" Text="LOGGING IN..." Classes="status-text" IsVisible="False"/>
				</Grid>

				<StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Spacing="6" VerticalAlignment="Center" Name="SupportPanel">
					<TextBlock Text="Visit our support:" Classes="footer-text" VerticalAlignment="Center" Name="SupportText"/>
					<Button Name="WebsiteButton" Background="Transparent" BorderThickness="0" Padding="8"
                            Cursor="Hand" Width="34" Height="34" VerticalAlignment="Center" CornerRadius="17"
                            ToolTip.Tip="https://t.me/William33821">
						<Viewbox Width="22" Height="22">
							<Canvas Width="24" Height="24">
								<Path Fill="#29AAED" Data="M12,0C5.373,0,0,5.373,0,12s5.373,12,12,12s12-5.373,12-12S18.627,0,12,0z M17.562,8.161c-0.18,1.897-0.962,6.502-1.359,8.627 c-0.168,0.9-0.5,1.201-0.82,1.23c-0.697,0.061-1.226-0.461-1.901-0.903c-1.056-0.692-1.653-1.123-2.678-1.799 c-1.185-0.781-0.417-1.21,0.258-1.911c0.177-0.184,3.247-2.977,3.307-3.23c0.007-0.032,0.015-0.15-0.056-0.212 s-0.174-0.041-0.248-0.024c-0.106,0.024-1.793,1.139-5.062,3.345c-0.479,0.329-0.913,0.489-1.302,0.481 c-0.428-0.009-1.252-0.242-1.865-0.442c-0.751-0.244-1.349-0.374-1.297-0.788c0.027-0.216,0.325-0.437,0.893-0.663 c3.498-1.524,5.831-2.529,6.998-3.015c3.332-1.386,4.025-1.627,4.477-1.635C17.328,7.212,17.672,7.695,17.562,8.161z"/>
							</Canvas>
						</Viewbox>
					</Button>
				</StackPanel>
			</StackPanel>
		</Grid>

		<Border Name="UpdateOverlay" Classes="update-overlay" IsVisible="False" ZIndex="1000">
			<StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Spacing="20" Margin="40">
				<TextBlock Name="UpdateTitle" Text="DOWNLOADING UPDATE" Classes="update-title"/>
				<TextBlock Name="UpdateStatus" Text="Downloading... 3648KB / 99364KB"
                           FontSize="14" Foreground="#CCCCCC" TextAlignment="Center" Margin="0 0 0 8"/>
				<ProgressBar Name="UpdateProgressBar" Classes="update-progress"
                             Minimum="0" Maximum="100" Value="26" Width="280"/>
				<TextBlock Name="UpdatePercentage" Text="26%"
                           FontSize="12" Foreground="#AAAAAA" HorizontalAlignment="Center"/>
			</StackPanel>
		</Border>
	</Grid>
</Window>