﻿using SamsungTool.Services.Interfaces;
using SamsungTool.Services.Implementations;
using System;
using System.Collections.Concurrent;

namespace SamsungTool.Services
{
    public sealed class ServiceContainer : IDisposable
    {
        private static readonly Lazy<ServiceContainer> _instance = new Lazy<ServiceContainer>(() => new ServiceContainer());
        private readonly ConcurrentDictionary<Type, object> _services = new ConcurrentDictionary<Type, object>();
        private readonly ConcurrentDictionary<Type, Func<object>> _factories = new ConcurrentDictionary<Type, Func<object>>();
        private bool _disposed = false;

        public static ServiceContainer Instance => _instance.Value;

        private ServiceContainer()
        {
            RegisterDefaults();
        }

        private void RegisterDefaults()
        {
            RegisterSingleton<IAdbService, AdbService>();
            RegisterSingleton<IMtpService, MtpService>();
            RegisterSingleton<IDownloadModeService, DownloadModeService>();
            RegisterSingleton<IFlashService, FlashService>();
            RegisterSingleton<IDownloadService, DownloadService>();
            RegisterSingleton<ILicenseService, LicenseService>();
            RegisterSingleton<ILogManager, LogManager>();
        }

        public void RegisterSingleton<TInterface, TImplementation>()
            where TImplementation : class, TInterface, new()
        {
            _factories[typeof(TInterface)] = () => new TImplementation();
        }

        public void RegisterSingleton<TInterface>(TInterface instance)
            where TInterface : class
        {
            _services[typeof(TInterface)] = instance;
        }

        public void RegisterSingleton<TInterface>(Func<TInterface> factory)
            where TInterface : class
        {
            _factories[typeof(TInterface)] = () => factory();
        }

        public TInterface GetService<TInterface>()
            where TInterface : class
        {
            var serviceType = typeof(TInterface);

            if (_services.TryGetValue(serviceType, out var existingService))
            {
                return (TInterface)existingService;
            }

            if (_factories.TryGetValue(serviceType, out var factory))
            {
                var newService = factory();
                _services[serviceType] = newService;
                return (TInterface)newService;
            }

            throw new InvalidOperationException($"Service of type {serviceType.Name} is not registered.");
        }

        public bool IsRegistered<TInterface>()
        {
            var serviceType = typeof(TInterface);
            return _services.ContainsKey(serviceType) || _factories.ContainsKey(serviceType);
        }

        public void Clear()
        {
            foreach (var service in _services.Values)
            {
                if (service is IDisposable disposable)
                {
                    try
                    {
                        disposable.Dispose();
                    }
                    catch
                    {
                    }
                }
            }

            _services.Clear();
            _factories.Clear();
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            Clear();
            _disposed = true;
        }
    }

    public static class ServiceLocator
    {
        public static TInterface GetService<TInterface>()
            where TInterface : class
        {
            return ServiceContainer.Instance.GetService<TInterface>();
        }

        public static void RegisterSingleton<TInterface, TImplementation>()
            where TImplementation : class, TInterface, new()
        {
            ServiceContainer.Instance.RegisterSingleton<TInterface, TImplementation>();
        }

        public static void RegisterSingleton<TInterface>(TInterface instance)
            where TInterface : class
        {
            ServiceContainer.Instance.RegisterSingleton(instance);
        }

        public static void RegisterSingleton<TInterface>(Func<TInterface> factory)
            where TInterface : class
        {
            ServiceContainer.Instance.RegisterSingleton(factory);
        }

        public static bool IsRegistered<TInterface>()
        {
            return ServiceContainer.Instance.IsRegistered<TInterface>();
        }
    }
}