﻿using System;
using System.Threading;
using System.Threading.Tasks;
using SamsungTool.Library;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbFrpService : AdbServiceBase
    {
        public async Task Run(CancellationToken cancellationToken = default)
        {
            // Use the new common operations utility for standard initialization
            bool initialized = await AdbCommonOperations.ExecuteStandardInitializationAsync(
                InitializeAsync,
                FindDeviceAsync,
                ReadDeviceInfoAsync,
                cancellationToken);

            if (!initialized)
            {
                return;
            }

            // Execute FRP removal using the common utility
            await AdbCommonOperations.ExecuteStandardFrpRemovalAsync(ADB, cancellationToken);

            // Execute reboot using the common utility
            await AdbCommonOperations.ExecuteStandardRebootAsync(ADB, cancellationToken: cancellationToken);

            // Use the new logging utility for operation completion
            AdbCommonOperations.LogOperationCompleted();
        }


    }
}