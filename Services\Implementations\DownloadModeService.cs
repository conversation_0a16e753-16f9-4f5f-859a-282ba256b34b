﻿using SamsungTool.Library;
using SamsungTool.Library.GUI;
using SamsungTool.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations
{
    public class DownloadModeService : IDownloadModeService, IDisposable
    {
        private bool _disposed = false;
        public static Downloader.Thread Downloader = new Downloader.Thread();

        public async Task ExecuteOperationAsync(string operation, CancellationToken cancellationToken = default)
        {
            await Task.Run(async () =>
            {
                cancellationToken.ThrowIfCancellationRequested();

                // Use the new device communication utility for port selection
                ServiceLoggingUtility.LogWaitingForDevice();
                string? Portname = await DeviceCommunicationUtility.GetSelectedPortNameAsync();

                cancellationToken.ThrowIfCancellationRequested();

                if (!DeviceCommunicationUtility.ValidatePortName(Portname))
                {
                    return;
                }

                // Use the new logging utility for device connection
                DeviceCommunicationUtility.LogDeviceConnection(Portname!);
                // Portname validated; suppress nullability warning by using local variable
                var portNameLocal = Portname!;
                ReadInfoOdin(portNameLocal);

                RichLogs("", Color.Silver, true);

                cancellationToken.ThrowIfCancellationRequested();

                if (operation == "factoryreset")
                {
                    RichLogs("Factory Reset...", Color.Silver, false);
                    string factory = await ExecuteRecoveryCommandAsync($"-e -d {SanitizeShellParameter(Portname!)}", cancellationToken);

                    if (factory.Contains("Close Connection"))
                    {
                        RichLogs("Okay", Color.LimeGreen, true);
                    }
                    else
                    {
                        RichLogs("Failed!", Color.IndianRed, true);
                    }
                }
                if (operation == "softbrick")
                {
                    RichLogs("Fix SOFTBRICK...", Color.Silver, false);
                    string softbrick = await ExecuteRecoveryCommandAsync($"-a fix.tar -d {SanitizeShellParameter(Portname!)}", cancellationToken);
                    if (softbrick.Contains("Close Connection"))
                    {
                        RichLogs("Okay", Color.LimeGreen, true);
                    }
                    else
                    {
                        RichLogs("Failed!", Color.IndianRed, true);
                    }
                }
                if (operation == "frpmtk")
                {
                    if (!await Downloader.FindAndSetDownloadMode())
                    {
                        throw new Exception("Invalid preocessing download mode!");
                    }
                    while (true)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        RichLogs("", Color.Black, true);
                        RichLogs("Erase Protection data...", Color.Silver, false);
                        if (!await Downloader.ReadBytes("111111111333"))
                        {
                            RichLogs("Failed!", Color.IndianRed, true);
                            break;
                        }
                        RichLogs("Okay", Color.LimeGreen, true);
                        RichLogs("Searching for devices...", Color.Silver, false);
                        await Task.Delay(7000, cancellationToken);

                        if (!await Downloader.FindAndSetDownloadMode())
                        {
                            RichLogs("Failed!", Color.IndianRed, true);
                            break;
                        }

                        RichLogs("Okay", Color.LimeGreen, true);

                        RichLogs("Remove FRP...", Color.Silver, false);
                        if (!await Downloader.WriteBytes("111111111333"))
                        {
                            RichLogs("Failed!", Color.IndianRed, true);
                            break;
                        }
                        RichLogs("Okay", Color.LimeGreen, true);
                        break;
                    }
                }

                RichLogs("", Color.Silver, true);
                RichLogs("Operation Completed", Color.LimeGreen, true);
            }, cancellationToken);
        }

        public string? ReadInfoOdin(string port)
        {
            if (string.IsNullOrEmpty(port))
            {
                RichLogs("Invalid port specified", System.Drawing.Color.IndianRed, true);
                return null;
            }

            try
            {
                using (var serialPort = new SerialPort(port)
                {
                    RtsEnable = true,
                    DtrEnable = true,
                    WriteBufferSize = 4096,
                    ReadTimeout = 3000
                })
                {
                    serialPort.Open();
                    if (!serialPort.IsOpen)
                    {
                        RichLogs("Failed to open port", System.Drawing.Color.IndianRed, true);
                        return null;
                    }

                    RichLogs("Reading information from device...", System.Drawing.Color.Silver, false);
                    serialPort.WriteLine("DVIF");

                    var respon = new StringBuilder();
                    var sw = Stopwatch.StartNew();
                    while (sw.ElapsedMilliseconds <= 3000)
                    {
                        if (!serialPort.IsOpen) break;
                        try
                        {
                            if (serialPort.BytesToRead > 0)
                            {
                                respon.Append(serialPort.ReadExisting());
                                if (respon.ToString().Contains("@#") || respon.ToString().Contains("ERROR"))
                                    break;
                            }
                            else Thread.Sleep(50);
                        }
                        catch (TimeoutException)
                        {
                            Thread.Sleep(50);
                        }
                        catch (Exception ex)
                        {
                            RichLogs($"Error reading from port: {ex.Message}", System.Drawing.Color.IndianRed, true);
                            return null;
                        }
                    }
                    sw.Stop();

                    if (!respon.ToString().Contains("@#"))
                    {
                        RichLogs("Failed! No valid response received", System.Drawing.Color.IndianRed, true);
                        return string.Empty;
                    }

                    RichLogs("Okay", System.Drawing.Color.LimeGreen, true);
                    RichLogs(string.Empty, System.Drawing.Color.LimeGreen, true);

                    var infoMap = new (string Label, string Key)[]
                    {
                ("MODEL",     "MODEL="),
                ("CSC",       "SALES="),
                ("AP version",";VER="),
                ("FWVER",     "FWVER="),
                ("UNIQUE NUMBER","UN="),
                ("STORAGE",   "CAPA="),
                ("VENDOR",    "VENDOR="),
                ("DISK",      "PRODUCT="),
                ("DID",       "DID="),
                ("TMU_TEMP",  "TMU_TEMP=")
                    };

                    const int labelWidth = 15;
                    string full = respon.ToString();
                    foreach (var (label, key) in infoMap)
                    {
                        int idx = full.IndexOf(key, StringComparison.Ordinal);
                        if (idx < 0) continue;
                        idx += key.Length;
                        int endIdx = full.IndexOfAny(new[] { ';', '@' }, idx);
                        string value = endIdx > idx
                            ? full.Substring(idx, endIdx - idx)
                            : string.Empty;
                        if (string.IsNullOrEmpty(value)) continue;

                        RichLogs($"{label.PadRight(labelWidth)}: ", System.Drawing.Color.Silver, false);

                        if (key == ";VER=")
                        {
                            var date = GetFirmwareDate(value);
                            RichLogs($"{value}{(string.IsNullOrEmpty(date) ? "" : " [" + date + "]")}",
                                     System.Drawing.Color.CornflowerBlue, true);
                        }
                        else
                        {
                            RichLogs(value, System.Drawing.Color.CornflowerBlue, true);
                        }
                    }

                    return respon.ToString();
                }
            }
            catch (UnauthorizedAccessException)
            {
                RichLogs("Port cannot be accessed, may be in use by another application", System.Drawing.Color.IndianRed, true);
            }
            catch (Exception ex)
            {
                RichLogs($"Error: {ex.Message}", System.Drawing.Color.IndianRed, true);
            }

            return null;
        }

        public async Task ReadInfoMTKFRPAsync()
        {
            RichLogs("Reading information from devices...", Color.Silver, false);
            var resp = await Downloader.Print();
            if (resp == null || !resp.Any())
            {
                RichLogs("Failed!", Color.IndianRed, true);
            }
            else
            {
                RichLogs("Okay", Color.LimeGreen, true);
            }
            RichLogs("", Color.LimeGreen, true);
            foreach (KeyValuePair<string, string> item in resp!)
            {
                switch (item.Key.ToLower())
                {
                    case "capa":
                        RichLogs("STORAGE : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "product":
                        RichLogs("DISK : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "model":
                        RichLogs("MODEL : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "fwver":
                        RichLogs("FWVER : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "vendor":
                        RichLogs("VENDOR : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "sales":
                        RichLogs("CSC : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "ver":
                        RichLogs("AP Version : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "did":
                        RichLogs("DID : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "un":
                        RichLogs("UNIQUE NUMBER : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "tmu_temp":
                        RichLogs("TMU_TEMP : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;

                    case "prov":
                        RichLogs("PROV : ", Color.Silver, false);
                        RichLogs(item.Value.ToUpper(), Color.CornflowerBlue, true);
                        break;
                }
            }
        }

        public async Task<string> ExecuteRecoveryCommandAsync(string cmdOptions, CancellationToken cancellationToken = default)
        {
            return await Task.Run(() =>
            {
                using (Process recoveryProcess = new Process())
                {
                    ProcessStartInfo startInfo = new ProcessStartInfo
                    {
                        FileName = Directory.GetCurrentDirectory() + "/data/recovery.exe",
                        WorkingDirectory = Path.Combine(Directory.GetCurrentDirectory(), "data"),
                        Arguments = cmdOptions,
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    };
                    recoveryProcess.StartInfo = startInfo;
                    string processOutput = "";

                    try
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        recoveryProcess.OutputDataReceived += (object sender, DataReceivedEventArgs e) =>
                        {
                            if (!string.IsNullOrEmpty(e.Data))
                            {
                                Console.WriteLine(e.Data);
                                processOutput += e.Data;
                            }
                        };

                        recoveryProcess.Start();
                        ProcessManager.RegisterProcess(recoveryProcess);

                        recoveryProcess.BeginOutputReadLine();

                        Stopwatch stopwatch = new Stopwatch();
                        stopwatch.Start();

                        while (!recoveryProcess.WaitForExit(100))
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            if (stopwatch.ElapsedMilliseconds > 5000 &&
                                !processOutput.Contains("Upload Binaries") &&
                                !processOutput.Contains("Check file :"))
                            {
                                try { recoveryProcess.Kill(); } catch { }
                                break;
                            }
                        }

                        return processOutput;
                    }
                    catch (OperationCanceledException)
                    {
                        try { recoveryProcess.Kill(); } catch { }
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error during Recovery process: {ex.Message}");
                        return $"ERROR: {ex.Message}";
                    }
                }
            }, cancellationToken);
        }

        private string GetFirmwareDate(string version)
        {
            if (string.IsNullOrEmpty(version) || version.Length < 3)
                return string.Empty;

            string year = GetYearFromVersion(version);
            string month = GetMonthFromVersion(version);
            string revision = GetRevFromVersion(version);

            if (year == "Unknown" || month == "Unknown")
                return string.Empty;

            return $"{month} {year}, {revision}";
        }

        private string GetYearFromVersion(string version)
        {
            if (version.Length >= 3)
            {
                char yearChar = version[version.Length - 3];
                switch (yearChar)
                {
                    case 'S': return "2019";
                    case 'T': return "2020";
                    case 'U': return "2021";
                    case 'V': return "2022";
                    case 'W': return "2023";
                    case 'X': return "2024";
                    case 'Y': return "2025";
                    case 'Z': return "2026";
                    default: return "Unknown";
                }
            }
            return "Unknown";
        }

        private string GetMonthFromVersion(string version)
        {
            if (version.Length >= 2)
            {
                char monthChar = version[version.Length - 2];
                switch (monthChar)
                {
                    case 'A': return "January";
                    case 'B': return "February";
                    case 'C': return "March";
                    case 'D': return "April";
                    case 'E': return "May";
                    case 'F': return "June";
                    case 'G': return "July";
                    case 'H': return "August";
                    case 'I': return "September";
                    case 'J': return "October";
                    case 'K': return "November";
                    case 'L': return "December";
                    default: return "Unknown";
                }
            }
            return "Unknown";
        }

        private string GetRevFromVersion(string version)
        {
            if (version.Length >= 1)
            {
                char revChar = version[version.Length - 1];
                return "Rev" + revChar;
            }
            return "Unknown";
        }

        private string SanitizeShellParameter(string parameter)
        {
            if (string.IsNullOrEmpty(parameter))
                return string.Empty;

            return Regex.Replace(parameter, @"[^A-Za-z0-9_\-./]", "");
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                try
                {
                    Downloader?.Dispose();
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }

            _disposed = true;
        }

        ~DownloadModeService()
        {
            Dispose(false);
        }
    }
}