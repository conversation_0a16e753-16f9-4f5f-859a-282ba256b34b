﻿using Avalonia.Controls.Documents;
using Avalonia.Media;
using Avalonia.Threading;
using System;
using System.Collections.Concurrent;
using System.Linq;

namespace SamsungTool.Library.GUI
{
    public class UI
    {
        private static class LogColors
        {
            public static readonly System.Drawing.Color DarkNormal = System.Drawing.Color.Silver;
            public static readonly System.Drawing.Color DarkSuccess = System.Drawing.Color.LimeGreen;
            public static readonly System.Drawing.Color DarkInfo = System.Drawing.Color.CornflowerBlue;
            public static readonly System.Drawing.Color DarkWarning = System.Drawing.Color.SkyBlue;
            public static readonly System.Drawing.Color DarkError = System.Drawing.Color.IndianRed;
            public static readonly System.Drawing.Color DarkCompleted = System.Drawing.Color.LimeGreen;

            public static readonly System.Drawing.Color LightNormal = System.Drawing.Color.FromArgb(60, 60, 60);
            public static readonly System.Drawing.Color LightSuccess = System.Drawing.Color.FromArgb(0, 110, 0);
            public static readonly System.Drawing.Color LightInfo = System.Drawing.Color.FromArgb(20, 70, 160);
            public static readonly System.Drawing.Color LightWarning = System.Drawing.Color.FromArgb(0, 90, 140);
            public static readonly System.Drawing.Color LightError = System.Drawing.Color.FromArgb(160, 0, 0);
            public static readonly System.Drawing.Color LightCompleted = System.Drawing.Color.FromArgb(0, 110, 0);

            private static readonly ConcurrentDictionary<System.Drawing.Color, System.Drawing.Color> ColorMappingCache =
                new ConcurrentDictionary<System.Drawing.Color, System.Drawing.Color>();

            public static void ClearCache()
            {
                ColorMappingCache.Clear();
            }

            public static System.Drawing.Color GetThemeAwareColor(System.Drawing.Color original, bool isDarkTheme)
            {
                if (isDarkTheme)
                    return original;

                if (ColorMappingCache.TryGetValue(original, out var mappedColor))
                    return mappedColor;

                System.Drawing.Color result;

                if (ColorEquals(original, DarkNormal))
                    result = LightNormal;
                else if (ColorEquals(original, DarkSuccess) || original.G > 200 && original.R < 100)
                    result = LightSuccess;
                else if (ColorEquals(original, DarkInfo) || (original.B > 200 && original.R < 150))
                    result = LightInfo;
                else if (ColorEquals(original, DarkWarning))
                    result = LightWarning;
                else if (ColorEquals(original, DarkError) || original.R > 200 && original.G < 150)
                    result = LightError;
                else if (IsBrightColor(original))
                    result = DarkenColor(original, 0.65f);
                else
                    result = original;

                ColorMappingCache[original] = result;
                return result;
            }

            private static bool ColorEquals(System.Drawing.Color c1, System.Drawing.Color c2, int tolerance = 5)
            {
                return Math.Abs(c1.R - c2.R) <= tolerance &&
                       Math.Abs(c1.G - c2.G) <= tolerance &&
                       Math.Abs(c1.B - c2.B) <= tolerance;
            }
        }

        public static void RichLogs(string text, object color, bool newLine = false)
        {
            try
            {
                System.Drawing.Color drawingColor;

                if (color is Avalonia.Media.Color avaloniaColor)
                {
                    drawingColor = System.Drawing.Color.FromArgb(
                        avaloniaColor.A,
                        avaloniaColor.R,
                        avaloniaColor.G,
                        avaloniaColor.B);
                }
                else if (color is System.Drawing.Color clr)
                {
                    drawingColor = clr;
                }
                else
                {
                    drawingColor = System.Drawing.Color.White;
                }

                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    try
                    {
                        if (MainWindow.GUI?.Richlogs != null)
                        {
                            bool isDarkTheme = true;
                            var adjustedColor = LogColors.GetThemeAwareColor(drawingColor, isDarkTheme);

                            string fullText = text;

                            AppendRichText(fullText, ConvertColor(adjustedColor), newLine);

                            if (MainWindow.GUI.LogScroll != null)
                            {
                                MainWindow.GUI.LogScroll.ScrollToEnd();
                            }

                            var lines = MainWindow.GUI.Richlogs.Text?.Split('\n');
                            if (lines != null && lines.Length > 1000)
                            {
                                var limitedLines = new string[1000];
                                Array.Copy(lines, lines.Length - 1000, limitedLines, 0, 1000);
                                MainWindow.GUI.Richlogs.Text = string.Join("\n", limitedLines);
                            }
                        }
                    }
                    catch
                    {
                    }
                });
            }
            catch
            {
            }
        }

        public static void RichLogsAppend(string text, object color, bool newLine = false)
        {
            try
            {
                string cleanText = text.TrimStart('\r', '\n');

                System.Drawing.Color drawingColor;

                if (color is Avalonia.Media.Color avaloniaColor)
                {
                    drawingColor = System.Drawing.Color.FromArgb(
                        avaloniaColor.A,
                        avaloniaColor.R,
                        avaloniaColor.G,
                        avaloniaColor.B);
                }
                else if (color is System.Drawing.Color clr)
                {
                    drawingColor = clr;
                }
                else
                {
                    drawingColor = System.Drawing.Color.White;
                }

                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    try
                    {
                        if (MainWindow.GUI?.Richlogs != null)
                        {
                            bool isDarkTheme = true;
                            var adjustedColor = LogColors.GetThemeAwareColor(drawingColor, isDarkTheme);

                            AppendRichText(cleanText, ConvertColor(adjustedColor), newLine);

                            if (MainWindow.GUI.LogScroll != null)
                            {
                                MainWindow.GUI.LogScroll.ScrollToEnd();
                            }
                        }
                    }
                    catch
                    {
                    }
                });
            }
            catch
            {
            }
        }

        private static bool IsBrightColor(System.Drawing.Color color)
        {
            double brightness = (0.299 * color.R + 0.587 * color.G + 0.114 * color.B);
            return brightness > 160;
        }

        private static System.Drawing.Color DarkenColor(System.Drawing.Color color, float factor)
        {
            return System.Drawing.Color.FromArgb(
                color.A,
                (byte)Math.Max(0, color.R * factor),
                (byte)Math.Max(0, color.G * factor),
                (byte)Math.Max(0, color.B * factor)
            );
        }

        private static Avalonia.Media.Color ConvertColor(System.Drawing.Color color)
        {
            return Avalonia.Media.Color.FromRgb(color.R, color.G, color.B);
        }

        private static void AppendRichText(string msg, Avalonia.Media.Color color, bool nextLine)
        {
            try
            {
                if (MainWindow.GUI?.Richlogs != null)
                {
                    var brush = new SolidColorBrush(color);

                    var run = new Run(msg)
                    {
                        Foreground = brush,
                        FontSize = 13.0
                    };

                    MainWindow.GUI?.Richlogs?.Inlines?.Add(run);

                    if (nextLine)
                        MainWindow.GUI?.Richlogs?.Inlines?.Add(new LineBreak());
                }
            }
            catch
            {
            }
        }

        public static void ClearLog()
        {
            try
            {
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    try
                    {
                        if (MainWindow.GUI?.Richlogs != null)
                        {
                            MainWindow.GUI.Richlogs.Inlines?.Clear();
                            MainWindow.GUI.Richlogs.Text = string.Empty;
                        }

                        if (MainWindow.GUI?.LogScroll != null)
                        {
                            MainWindow.GUI.LogScroll.ScrollToHome();
                        }
                    }
                    catch
                    {
                    }
                });
            }
            catch
            {
            }
        }

        public static void RefreshLogColorsForTheme()
        {
            try
            {
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    try
                    {
                        if (MainWindow.GUI?.Richlogs != null)
                        {
                            bool isDarkTheme = true;

                            LogColors.ClearCache();

                            foreach (var inline in MainWindow.GUI.Richlogs.Inlines ?? Enumerable.Empty<Avalonia.Controls.Documents.Inline>())
                            {
                                if (inline is Run run && run.Foreground is SolidColorBrush brush)
                                {
                                    var originalColor = System.Drawing.Color.FromArgb(
                                        brush.Color.A, brush.Color.R, brush.Color.G, brush.Color.B);

                                    var adjustedColor = LogColors.GetThemeAwareColor(originalColor, isDarkTheme);

                                    run.Foreground = new SolidColorBrush(ConvertColor(adjustedColor));
                                }
                            }
                        }
                    }
                    catch
                    {
                    }
                });
            }
            catch
            {
            }
        }

        public static void ProcessBar1(int process)
        {
            try
            {
                int val = process;
                if (val > 100)
                    val = 100;

                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    try
                    {
                        if (MainWindow.GUI?.Loading != null)
                        {
                            MainWindow.GUI.Loading.Value = Math.Max(0, Math.Min(100, val));

                            if (!MainWindow.GUI.Loading.IsVisible && val > 0)
                            {
                                MainWindow.GUI.Loading.IsVisible = true;
                            }
                        }
                    }
                    catch
                    {
                    }
                });
            }
            catch
            {
            }
        }

        public static void ProcessBar1(long process, long total)
        {
            try
            {
                int val = Convert.ToInt32(process * 100 / (double)total);
                if (val > 100)
                    val = 100;

                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    try
                    {
                        if (MainWindow.GUI?.Loading != null)
                        {
                            MainWindow.GUI.Loading.Value = Math.Max(0, Math.Min(100, val));
                        }
                    }
                    catch
                    {
                    }
                });
            }
            catch
            {
            }
        }

        public static void UpdateOperationStatus(string status)
        {
            try
            {
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    try
                    {
                        if (MainWindow.GUI?.operationStatus != null)
                        {
                            MainWindow.GUI.operationStatus.Text = status ?? "Ready";
                        }
                    }
                    catch
                    {
                    }
                });
            }
            catch
            {
            }
        }

        public static void SetProgressBarVisible(bool visible)
        {
            try
            {
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    try
                    {
                        if (MainWindow.GUI?.Loading != null)
                        {
                            MainWindow.GUI.Loading.IsVisible = visible;
                            if (!visible)
                            {
                                MainWindow.GUI.Loading.Value = 0;
                            }
                        }
                    }
                    catch
                    {
                    }
                });
            }
            catch
            {
            }
        }

        public static void LogSuccess(string message)
        {
            RichLogs(message, System.Drawing.Color.LimeGreen, true);
        }

        public static void LogError(string message)
        {
            RichLogs(message, System.Drawing.Color.IndianRed, true);
        }

        public static void LogWarning(string message)
        {
            RichLogs(message, System.Drawing.Color.Orange, true);
        }

        public static void LogInfo(string message)
        {
            RichLogs(message, System.Drawing.Color.Silver, true);
        }

        public static void LogProgress(string message)
        {
            RichLogs(message, System.Drawing.Color.SkyBlue, true);
        }
    }
}