﻿using SamsungTool.Library;
using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbRawShellService : AdbServiceBase
    {
        public async Task Run(string commandsText, CancellationToken cancellationToken = default)
        {
            if (!await InitializeAsync(cancellationToken))
            {
                return;
            }

            if (!await FindDeviceAsync(cancellationToken))
            {
                return;
            }

            await ExecuteRawShellCommandsAsync(commandsText, cancellationToken);

            RichLogs("", Color.Silver, true);
            RichLogs("Operation Completed", Color.LimeGreen, true);
        }

        private async Task ExecuteRawShellCommandsAsync(string commandsText, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(SNADB))
            {
                RichLogs("No device selected", Color.IndianRed, true);
                return;
            }

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Preparing to execute commands...", Color.Silver);
                string tempFileName = Path.GetTempFileName();
                File.WriteAllText(tempFileName, commandsText.Replace("\r\n", "\n"));

                Process process = new Process();
                try
                {
                    process.StartInfo.FileName = Path.Combine(Directory.GetCurrentDirectory(), "Data", "adb.exe");
                    process.StartInfo.Arguments = "-s " + SanitizeShellParameter(SNADB) + " shell";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.CreateNoWindow = true;
                    process.StartInfo.RedirectStandardInput = true;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    RichLogs("Okay", Color.LimeGreen, true);
                    RichLogs("Executing commands...", Color.Silver, false);
                    process.Start();

                    ProcessManager.RegisterProcess(process);

                    string[] commandLines = File.ReadAllLines(tempFileName);

                    if (process.StandardInput.BaseStream.CanWrite)
                    {
                        foreach (string line in commandLines)
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            if (!string.IsNullOrWhiteSpace(line))
                            {
                                process.StandardInput.WriteLine(SanitizeShellParameter(line));
                            }
                        }
                        process.StandardInput.WriteLine("exit");
                        process.StandardInput.Flush();
                        process.StandardInput.Close();
                    }

                    await Task.Run(() => process.WaitForExit(30000), cancellationToken);

                    try
                    {
                        File.Delete(tempFileName);
                    }
                    catch
                    {
                    }
                }
                finally
                {
                    if (process != null)
                    {
                        ((IDisposable)process).Dispose();
                    }
                    RichLogs("Okay", Color.LimeGreen, true);
                }
            }
            catch (OperationCanceledException)
            {
                RichLogs("Command execution was cancelled", Color.Orange, true);
                throw;
            }
            catch (System.Exception ex)
            {
                RichLogs("Failed", Color.IndianRed, true);
                RichLogs("Error: " + ex.Message, Color.IndianRed, true);
            }
        }
    }
}