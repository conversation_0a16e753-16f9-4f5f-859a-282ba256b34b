﻿using Avalonia.Threading;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO.Ports;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Library.GUI
{
    public class USB
    {
        public static List<COMINFO> ListDevices = new List<COMINFO>();
        public static SerialPort? Ports = new SerialPort();
        public static readonly Stopwatch Watch = new Stopwatch();
        public static bool IsSwitchingPort = false;
        public static long Delta = 0;
        private static readonly SemaphoreSlim _updateLock = new SemaphoreSlim(1, 1);
        public static bool Open = false;
        public static string? Vid { get; set; }
        public static string? Pid { get; set; }
        public static string? Rev { get; set; }
        public static string? State { get; set; }
        public static int Com { get; set; }

        public class COMINFO
        {
            public string? Name { get; set; }
            public string? Hwid { get; set; }
            public string? Comport { get; set; }
            public int Type { get; set; }
        }

        public static void GetComInfo()
        {
            Watch.Start();
            Task.Run(async () => await UpdateList());
        }

        public static void DisposeWatcher()
        {
            try
            {
                _updateLock?.Dispose();
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        public static async Task UpdateList()
        {
            if (!await _updateLock.WaitAsync(100))
            {
                return;
            }

            try
            {
                var list = new List<COMINFO>();
                try
                {
                    // Get Samsung devices using PowerShell command
                    var samsungPorts = await GetSamsungComPortsAsync();
                    
                    foreach (var samsungPort in samsungPorts)
                    {
                        var portNum = samsungPort.PortName.Replace("COM", string.Empty);
                        
                        var comInfo = new COMINFO
                        {
                            Name = samsungPort.Description,
                            Comport = portNum,
                            Type = GetDeviceType(samsungPort.Description),
                            Hwid = samsungPort.HardwareId ?? ""
                        };

                        list.Add(comInfo);
                    }

                    // Remove duplicates by COM port
                    list = list.GroupBy(x => x.Comport).Select(g => g.First()).ToList();

                    if (list.Count != ListDevices.Count || !ListDevicesEqual(list, ListDevices))
                    {
                        ListDevices = list;
                        try
                        {
                            UpdateComboPort(ListDevices);
                        }
                        catch (Exception)
                        {
                            // Silent error handling for production
                        }
                    }
                }
                catch (Exception)
                {
                    // Silent error handling for production
                }
            }
            finally
            {
                _updateLock.Release();
            }
        }

        private static bool ListDevicesEqual(List<COMINFO> list1, List<COMINFO> list2)
        {
            if (list1.Count != list2.Count)
                return false;

            for (int i = 0; i < list1.Count; i++)
            {
                if (list1[i].Comport != list2[i].Comport ||
                    list1[i].Name != list2[i].Name ||
                    list1[i].Hwid != list2[i].Hwid ||
                    list1[i].Type != list2[i].Type)
                    return false;
            }

            return true;
        }

        public static void UpdateComboPort(List<COMINFO> list)
        {
            var firstItem = string.Empty;

            Dispatcher.UIThread.InvokeAsync(() =>
            {
                try
                {
                    if (MainWindow.GUI?.cmbCOM == null)
                        return;

                    var currentSelectedText = MainWindow.GUI.cmbCOM.SelectedItem?.ToString();

                    if (list.Count < MainWindow.GUI.cmbCOM.Items.Count)
                    {
                        MainWindow.GUI.cmbCOM.SelectedItem = null;
                    }

                    MainWindow.GUI.cmbCOM.Items.Clear();

                    foreach (var item in list)
                    {
                        var typePrefix = string.Empty;

                        if (item.Type == 1)
                        {
                            typePrefix = "[MTP] ";
                        }
                        else if (item.Type == 2)
                        {
                            typePrefix = "[DLM] ";
                        }
                        else if (item.Type == 3)
                        {
                            typePrefix = "[DIAG] ";
                        }

                        var displayName = typePrefix + (item.Name ?? $"Samsung Mobile USB Modem (COM{item.Comport})");
                        MainWindow.GUI.cmbCOM.Items.Add(displayName);

                        if (string.IsNullOrEmpty(firstItem))
                        {
                            firstItem = displayName;
                        }

                        if (!string.IsNullOrEmpty(currentSelectedText) && 
                            (currentSelectedText.Contains($"COM{item.Comport}") || currentSelectedText.Contains(displayName)))
                        {
                            MainWindow.GUI.cmbCOM.SelectedItem = displayName;
                        }
                    }

                    if (MainWindow.GUI.cmbCOM.SelectedItem == null)
                    {
                        if (!string.IsNullOrEmpty(firstItem))
                        {
                            MainWindow.GUI.cmbCOM.SelectedItem = firstItem;
                        }
                        else if (list.Count > 0)
                        {
                            MainWindow.GUI.cmbCOM.SelectedIndex = 0;
                        }
                    }

                    HandlePortSelection();
                }
                catch
                {
                }
            });
        }

        private static void HandlePortSelection()
        {
            try
            {
                var comboText = MainWindow.GUI?.cmbCOM?.SelectedItem?.ToString();

                if (!string.IsNullOrEmpty(comboText))
                {
                    var match = Regex.Match(comboText, @"COM(\d+)", RegexOptions.IgnoreCase | RegexOptions.Compiled);

                    if (match.Success)
                    {
                        var comNumber = match.Groups[1].Value;
                        if (int.TryParse(comNumber, out int comInt))
                        {
                            Com = comInt;
                        }
                    }
                    else
                    {
                        var numberMatch = Regex.Match(comboText, @"#(\d+)", RegexOptions.Compiled);
                        if (numberMatch.Success && int.TryParse(numberMatch.Groups[1].Value, out int fallbackCom))
                        {
                            Com = fallbackCom;
                        }
                    }

                    Vid = "04E8";
                    Pid = "0000";
                    Rev = "0000";
                    State = comboText + "\n[VID : " + Vid + " PID : " + Pid + " REV : " + Rev + "] - Connected!";

                    if (!string.IsNullOrEmpty(Vid) && Com > 0)
                    {
                        Open = true;
                    }
                    else
                    {
                        Open = false;
                    }
                }
                else
                {
                    Open = false;
                    if (State != null && State.Contains("Connected!"))
                    {
                        State = State.Replace("Connected!", "Disconnected!");
                    }
                    Vid = "";
                    Pid = "";
                    Rev = "";
                    Com = 0;
                }
            }
            catch
            {
            }
        }

        public static string BetweenStrings(string text, string start, string end)
        {
            var startIndex = text.IndexOf(start, StringComparison.Ordinal);
            if (startIndex == -1) return string.Empty;
            
            startIndex += start.Length;
            
            if (string.Equals(end, string.Empty, StringComparison.Ordinal))
            {
                return text.Substring(startIndex);
            }
            
            var endIndex = text.IndexOf(end, startIndex, StringComparison.Ordinal);
            if (endIndex == -1) return string.Empty;
            
            return text.Substring(startIndex, endIndex - startIndex);
        }

        private static async Task<List<ComPortInfo>> GetSamsungComPortsAsync()
        {
            var samsungPorts = new List<ComPortInfo>();
            
            try
            {
                // First method: Use PowerShell to get WMI serial port information
                var samsungWmiPorts = await GetSamsungPortsFromWmiAsync();
                if (samsungWmiPorts.Count > 0)
                {
                    samsungPorts.AddRange(samsungWmiPorts);
                }

                // Second method: Check available ports and validate through device manager
                if (samsungPorts.Count == 0)
                {
                    var validatedPorts = await GetSamsungPortsFromDeviceManagerAsync();
                    samsungPorts.AddRange(validatedPorts);
                }

                // Remove duplicates
                var uniquePorts = samsungPorts
                    .GroupBy(p => p.PortName)
                    .Select(g => g.First())
                    .ToList();

                return uniquePorts;
            }
            catch
            {
                return new List<ComPortInfo>();
            }
        }

        private static async Task<List<ComPortInfo>> GetSamsungPortsFromWmiAsync()
        {
            var samsungPorts = new List<ComPortInfo>();
            
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = "-Command \"Get-WmiObject -Class Win32_SerialPort | Where-Object { $_.Name -like '*SAMSUNG*' } | Select-Object Name, DeviceID, Status | Format-Table -AutoSize\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using (var process = new Process { StartInfo = startInfo })
                {
                    process.Start();
                    
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();
                    
                    await Task.Run(() => process.WaitForExit(5000));

                    if (process.ExitCode == 0 && !string.IsNullOrWhiteSpace(output))
                    {
                        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                        
                        foreach (var line in lines)
                        {
                            if (line.Contains("SAMSUNG", StringComparison.OrdinalIgnoreCase) && 
                                line.Contains("COM", StringComparison.OrdinalIgnoreCase))
                            {
                                var comMatch = Regex.Match(line, @"COM(\d+)", RegexOptions.IgnoreCase);
                                if (comMatch.Success)
                                {
                                    var comPort = comMatch.Value;
                                    var deviceName = ExtractDeviceName(line);
                                    
                                    if (!string.IsNullOrEmpty(deviceName))
                                    {
                                        samsungPorts.Add(new ComPortInfo
                                        {
                                            PortName = comPort,
                                            Description = deviceName,
                                            HardwareId = "SAMSUNG_WMI"
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch
            {
                // Silent fail for PowerShell errors
            }

            return samsungPorts;
        }

        private static async Task<List<ComPortInfo>> GetSamsungPortsFromDeviceManagerAsync()
        {
            var samsungPorts = new List<ComPortInfo>();
            
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = "-Command \"Get-WmiObject -Class Win32_PnPEntity | Where-Object { $_.Name -like '*SAMSUNG*' -and $_.Name -like '*COM*' } | Select-Object Name, DeviceID | Format-Table -AutoSize\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using (var process = new Process { StartInfo = startInfo })
                {
                    process.Start();
                    
                    var output = await process.StandardOutput.ReadToEndAsync();
                    await Task.Run(() => process.WaitForExit(5000));

                    if (process.ExitCode == 0 && !string.IsNullOrWhiteSpace(output))
                    {
                        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                        
                        foreach (var line in lines)
                        {
                            if (line.Contains("SAMSUNG", StringComparison.OrdinalIgnoreCase) && 
                                line.Contains("COM", StringComparison.OrdinalIgnoreCase))
                            {
                                var comMatch = Regex.Match(line, @"COM(\d+)", RegexOptions.IgnoreCase);
                                if (comMatch.Success)
                                {
                                    var comPort = comMatch.Value;
                                    
                                    // Verify COM port actually exists
                                    var availablePorts = SerialPort.GetPortNames();
                                    if (availablePorts.Contains(comPort, StringComparer.OrdinalIgnoreCase))
                                    {
                                        var deviceName = ExtractDeviceName(line);
                                        
                                        if (!string.IsNullOrEmpty(deviceName))
                                        {
                                            samsungPorts.Add(new ComPortInfo
                                            {
                                                PortName = comPort,
                                                Description = deviceName,
                                                HardwareId = "SAMSUNG_PNP"
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch
            {
                // Silent fail for PowerShell errors
            }

            return samsungPorts;
        }

        private static string ExtractDeviceName(string line)
        {
            try
            {
                // Clean up the line and extract device name
                var cleanLine = line.Trim();
                
                // Remove extra whitespace
                cleanLine = Regex.Replace(cleanLine, @"\s+", " ");
                
                // Extract the first part which should be the device name
                var parts = cleanLine.Split(new[] { "COM" }, StringSplitOptions.None);
                if (parts.Length >= 2)
                {
                    var devicePart = parts[0].Trim();
                    var comPart = "COM" + parts[1].Split(' ')[0];
                    
                    if (!string.IsNullOrWhiteSpace(devicePart))
                    {
                        return $"{devicePart} ({comPart})";
                    }
                }

                // Fallback: try to extract Samsung device name pattern
                var samsungMatch = Regex.Match(cleanLine, @"SAMSUNG[^,]+(?:\s+#\d+)?", RegexOptions.IgnoreCase);
                if (samsungMatch.Success)
                {
                    var comMatch = Regex.Match(cleanLine, @"COM\d+", RegexOptions.IgnoreCase);
                    if (comMatch.Success)
                    {
                        return $"{samsungMatch.Value} ({comMatch.Value})";
                    }
                }

                return string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        private static int GetDeviceType(string? description)
        {
            if (string.IsNullOrEmpty(description))
                return 0;

            var lowerDesc = description.ToLowerInvariant();
            
            if (lowerDesc.Contains("mtp", StringComparison.OrdinalIgnoreCase) || 
                lowerDesc.Contains("media", StringComparison.OrdinalIgnoreCase))
                return 1; // MTP
            else if (lowerDesc.Contains("download", StringComparison.OrdinalIgnoreCase) || 
                     lowerDesc.Contains("odin", StringComparison.OrdinalIgnoreCase))
                return 2; // Download Mode
            else if (lowerDesc.Contains("diag", StringComparison.OrdinalIgnoreCase))
                return 3; // Diagnostic
            else
                return 0; // Default/Unknown
        }

        private class ComPortInfo
        {
            public string PortName { get; set; } = "";
            public string? Description { get; set; } = "";
            public string? HardwareId { get; set; }
        }

        // Test method để verify detection logic
        public static async Task<string> TestSamsungDetectionAsync()
        {
            var testResult = new System.Text.StringBuilder();
            testResult.AppendLine("=== Samsung Device Detection Test ===");
            
            try
            {
                // Test WMI Serial Port detection
                testResult.AppendLine("\n1. Testing WMI Win32_SerialPort:");
                var wmiPorts = await GetSamsungPortsFromWmiAsync();
                if (wmiPorts.Count > 0)
                {
                    foreach (var port in wmiPorts)
                    {
                        testResult.AppendLine($"   Found: {port.Description} ({port.PortName})");
                    }
                }
                else
                {
                    testResult.AppendLine("   No Samsung serial ports found via WMI");
                }

                // Test PnP Entity detection
                testResult.AppendLine("\n2. Testing WMI Win32_PnPEntity:");
                var pnpPorts = await GetSamsungPortsFromDeviceManagerAsync();
                if (pnpPorts.Count > 0)
                {
                    foreach (var port in pnpPorts)
                    {
                        testResult.AppendLine($"   Found: {port.Description} ({port.PortName})");
                    }
                }
                else
                {
                    testResult.AppendLine("   No Samsung PnP devices found");
                }

                // Show all available COM ports for comparison
                testResult.AppendLine("\n3. All available COM ports:");
                var allPorts = SerialPort.GetPortNames();
                foreach (var port in allPorts)
                {
                    testResult.AppendLine($"   {port}");
                }

                // Test raw PowerShell output for debugging
                testResult.AppendLine("\n4. Raw PowerShell output for Win32_SerialPort:");
                var rawOutput = await GetRawSerialPortOutputAsync();
                if (!string.IsNullOrEmpty(rawOutput))
                {
                    testResult.AppendLine(rawOutput);
                }
                else
                {
                    testResult.AppendLine("   No output from PowerShell");
                }
            }
            catch (Exception ex)
            {
                testResult.AppendLine($"\nTest failed with error: {ex.Message}");
            }

            return testResult.ToString();
        }

        private static async Task<string> GetRawSerialPortOutputAsync()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = "-Command \"Get-WmiObject -Class Win32_SerialPort | Select-Object Name, DeviceID, Status | Format-Table -AutoSize\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using (var process = new Process { StartInfo = startInfo })
                {
                    process.Start();
                    
                    var output = await process.StandardOutput.ReadToEndAsync();
                    await Task.Run(() => process.WaitForExit(5000));

                    return output;
                }
            }
            catch
            {
                return "";
            }
        }

        // Method để log detection results khi debug
        public static async Task LogDetectionResultsAsync()
        {
            try
            {
                var testResults = await TestSamsungDetectionAsync();
                
                // Log to debug output nếu đang trong debug mode
                #if DEBUG
                System.Diagnostics.Debug.WriteLine(testResults);
                #endif

                // Có thể thêm vào UI log nếu cần
                if (MainWindow.GUI != null)
                {
                    Dispatcher.UIThread.Post(() =>
                    {
                        try
                        {
                            // Uncomment next line if you want to see test results in UI logs
                            // MainWindow.GUI.Richlogs?.Text += testResults + "\n";
                        }
                        catch { }
                    });
                }
            }
            catch
            {
                // Silent error handling
            }
        }
    }
}