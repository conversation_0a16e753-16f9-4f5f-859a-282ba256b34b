using Avalonia;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Library
{
    public enum PartitionType
    {
        AP, BL, CP, CSC, USERDATA
    }

    public class FlashEntry
    {
        public PartitionType Type { get; set; }
        public string? FilePath { get; set; }
    }

    public class FlashOptions
    {
        public bool AutoReboot { get; set; } = true;
        public bool ValidateMd5 { get; set; } = true;
        public bool NandErase { get; set; }
    }

    public class ProgressReport
    {
        public string? CurrentFile { get; set; }
        public long BytesTransferred { get; set; }
        public long TotalBytes { get; set; }
        public int PercentComplete { get; set; }
    }

    public class ObjectFirmwares
    {
        public string? name { get; set; }
        public long size { get; set; }
    }

    public sealed class OdinService : IDisposable
    {
        private static bool isFlashing = false;
        // private static long flashFlashedSize;
        private static long flashTotalSize;
        private static List<ObjectFirmwares> firmwareinfo = new List<ObjectFirmwares>();

        private IProgress<ProgressReport>? _currentProgress;
        private string _currentFlashingFile = "";
        private HashSet<string> _completedFiles = new HashSet<string>();
        private HashSet<string> _reportedMessages = new HashSet<string>();
        private Dictionary<string, string> _deviceInfo = new Dictionary<string, string>();
        private bool _isCollectingDeviceInfo = false;
        private string _currentInfoKey = "";
        private bool _deviceInfoPrinted = false;
        private Dictionary<string, bool> _fileCompleted = new Dictionary<string, bool>();
        private Dictionary<string, bool> _fileFailed = new Dictionary<string, bool>();
        private bool _disposed = false;

        public OdinService()
        {
            // Constructor - no SharpOdinClient initialization needed
        }

        private bool ProcessDeviceInfo(string text)
        {
            string[] deviceInfoLabels = new[] {
                "Model Number:", "Unique Id:", "Capa Number:", "vendor:",
                "Firmware Version:", "Product Id:", "Provision:", "Sales Code:",
                "Build Number:", "Did Number:", "Tmu Number:"
            };

            if (_deviceInfoPrinted)
            {
                if (deviceInfoLabels.Any(l => text.Contains(l)))
                {
                    return true;
                }
                return false;
            }

            foreach (string label in deviceInfoLabels)
            {
                if (text.Trim() == label)
                {
                    _isCollectingDeviceInfo = true;
                    _currentInfoKey = label.TrimEnd(':');
                    return true;
                }
            }

            if (_isCollectingDeviceInfo && !string.IsNullOrEmpty(_currentInfoKey))
            {
                string value = text.Trim();
                _deviceInfo[_currentInfoKey] = value;

                RichLogs($"{_currentInfoKey}: ", Color.Silver, false);
                RichLogs(value, Color.CornflowerBlue, true);

                _currentInfoKey = "";

                if (text.Trim() == deviceInfoLabels.Last().TrimEnd(':'))
                {
                    _isCollectingDeviceInfo = false;
                }
                return true;
            }

            return false;
        }

        public async Task<IReadOnlyDictionary<PartitionType, string>> FlashAsync(
            string port,
            FlashOptions options,
            IEnumerable<FlashEntry> files,
            IProgress<ProgressReport> progress,
            CancellationToken token)
        {
            if (isFlashing)
            {
                RichLogs("System is busy. Flash operation in progress.", Color.IndianRed, true);
                throw new InvalidOperationException("Flash operation already in progress");
            }

            try
            {
                isFlashing = true;
                _currentProgress = progress;
                _currentFlashingFile = "";
                _completedFiles.Clear();
                _reportedMessages.Clear();
                _deviceInfo.Clear();
                _isCollectingDeviceInfo = false;
                _currentInfoKey = "";
                _deviceInfoPrinted = false;
                _fileCompleted.Clear();
                _fileFailed.Clear();

                token.ThrowIfCancellationRequested();

                RichLogs($"Connecting to ", Color.Silver, false);
                RichLogs($"{port}...", Color.CornflowerBlue, false);
                RichLogs("Okay", Color.LimeGreen, true);

                // Build flash command
                string flashCommand = BuildFlashCommand(port, options, files);

                if (string.IsNullOrEmpty(flashCommand))
                {
                    throw new InvalidOperationException("No firmware files to flash");
                }

                // Get device info first
                await Task.Run(() =>
                {
                    string deviceInfo = ReadInfoOdin(port);
                    _deviceInfoPrinted = true;
                }, token);

                // Initialize firmware info
                var selectedFiles = files.Where(f => !string.IsNullOrEmpty(f.FilePath)).Select(f => f.FilePath!).ToList();
                await Task.Run(() =>
                {
                    flashTotalSize = 0L;
                    // flashFlashedSize = 0L;
                    List<ObjectFirmwares> firmwareList = new List<ObjectFirmwares>();

                    RichLogs("Initializing flash firmware...", Color.Silver, false);

                    if (selectedFiles != null && selectedFiles.Any())
                    {
                        foreach (string firmwareFile in selectedFiles)
                        {
                            ExtractFirmware(firmwareFile, ref flashTotalSize, ref firmwareList);
                        }
                    }

                    firmwareinfo = firmwareList;
                    RichLogs("Okay", Color.LimeGreen, true);
                }, token);

                RichLogs("Starting firmware flash...", Color.Silver, true);

                // Execute flash command
                string flashResult = await Task.Run(() =>
                    ExecuteOdinCommand(flashCommand, 20000, true, options.ValidateMd5), token);

                // Process result
                ProcessFlashResult(flashResult);

                if (options.AutoReboot)
                {
                    RichLogs("", Color.Silver, true);
                    RichLogs("Rebooting device...", Color.Silver, false);
                    // Device reboot is handled by the exe tool
                    RichLogs("Okay", Color.LimeGreen, true);
                }

                var result = new Dictionary<PartitionType, string>();
                foreach (var file in files)
                {
                    result[file.Type] = "Success";
                }

                return result;
            }
            finally
            {
                isFlashing = false;
            }
        }

        private string BuildFlashCommand(string port, FlashOptions options, IEnumerable<FlashEntry> files)
        {
            string flashCommand = string.Empty;

            if (!options.AutoReboot)
            {
                flashCommand += " --noreboot";
            }
            if (!options.ValidateMd5)
            {
                flashCommand += " --ignore-md5";
            }
            if (options.NandErase)
            {
                flashCommand += " -e";
            }

            foreach (var file in files)
            {
                if (string.IsNullOrEmpty(file.FilePath) || !File.Exists(file.FilePath))
                    continue;

                switch (file.Type)
                {
                    case PartitionType.BL:
                        flashCommand += $" -b \"{file.FilePath}\"";
                        break;
                    case PartitionType.AP:
                        flashCommand += $" -a \"{file.FilePath}\"";
                        break;
                    case PartitionType.CP:
                        flashCommand += $" -c \"{file.FilePath}\"";
                        break;
                    case PartitionType.CSC:
                        flashCommand += $" -s \"{file.FilePath}\"";
                        break;
                    case PartitionType.USERDATA:
                        flashCommand += $" -u \"{file.FilePath}\"";
                        break;
                }
            }

            flashCommand += $" -d {port}";
            return flashCommand;
        }

        private void ProcessFlashResult(string flashResult)
        {
            // Improved result processing with better error detection
            if (string.IsNullOrEmpty(flashResult))
            {
                RichLogs("No output received from recovery tool", Color.IndianRed, true);
                throw new InvalidOperationException("No output received from recovery tool");
            }

            // Check for specific failure patterns
            if (flashResult.Contains("FAIL!") || flashResult.Contains("ERROR") || flashResult.Contains("Failed"))
            {
                string failureReason = "";

                if (flashResult.Contains("FAIL!"))
                {
                    failureReason = ExtractSubstring(flashResult, "FAIL!", "\r\n");
                }
                else if (flashResult.Contains("ERROR"))
                {
                    failureReason = ExtractSubstring(flashResult, "ERROR", "\r\n");
                }
                else if (flashResult.Contains("Failed"))
                {
                    failureReason = ExtractSubstring(flashResult, "Failed", "\r\n");
                }

                // Mark current file as failed if we have one
                if (!string.IsNullOrEmpty(_currentFlashingFile))
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    _fileFailed[_currentFlashingFile] = true;
                }

                RichLogs("Operation failed", Color.IndianRed, true);
                if (!string.IsNullOrEmpty(failureReason))
                {
                    RichLogs(failureReason.Trim(), Color.IndianRed, true);
                    throw new InvalidOperationException($"Flash failed: {failureReason.Trim()}");
                }
                else
                {
                    throw new InvalidOperationException("Flash failed: Unknown error");
                }
            }

            // Check for success patterns
            if (flashResult.Contains("Close Connection") ||
                flashResult.Contains("SUCCESS") ||
                flashResult.Contains("Completed") ||
                flashResult.Contains("100%"))
            {
                // Mark any remaining file as completed
                if (!string.IsNullOrEmpty(_currentFlashingFile))
                {
                    RichLogs("Okay", Color.LimeGreen, true);
                }
                
                RichLogs("", Color.Silver, true);
                RichLogs("Operation Completed", Color.LimeGreen, true);
                return;
            }

            // Check if the tool is still running or waiting
            if (flashResult.Contains("Upload Binaries") ||
                flashResult.Contains("Check file :") ||
                flashResult.Contains("Waiting"))
            {
                RichLogs("Process may still be running...", Color.Orange, true);
                throw new InvalidOperationException("Flash process may have been interrupted");
            }

            // If we get here, we don't recognize the output
            RichLogs("Unrecognized output from recovery tool:", Color.Orange, true);
            RichLogs(flashResult, Color.Gray, true);
            throw new InvalidOperationException($"Unrecognized output from recovery tool. Please check the logs.");
        }

        private string ReadInfoOdin(string port)
        {
            // Execute device info command - adjust parameters as needed for your recovery.exe
            return ExecuteOdinCommand($"-d {port} --info", 5000, false, false);
        }

        private string ExecuteOdinCommand(string cmdOptions, int timeout = 5000, bool flash = false, bool checkMd5 = false)
        {
            // Try multiple possible locations for recovery.exe
            string[] possiblePaths = new[]
            {
        Path.Combine(Directory.GetCurrentDirectory(), "data", "recovery.exe"),
        Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "data", "recovery.exe"),
        Path.Combine(Environment.CurrentDirectory, "Tools", "recovery.exe"),
        Path.Combine(Directory.GetCurrentDirectory(), "Tools", "recovery.exe")
    };

            string recoveryPath = "";
            foreach (string path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    recoveryPath = path;
                    break;
                }
            }

            if (string.IsNullOrEmpty(recoveryPath))
            {
                string searchedPaths = string.Join("\n- ", possiblePaths);
                string errorMessage = $"Recovery tool not found. Searched in:\n- {searchedPaths}";
                RichLogs(errorMessage, Color.IndianRed, true);
                RichLogs("", Color.Silver, true);
                RichLogs("Please ensure recovery.exe is placed in one of the above directories.", Color.Orange, true);
                return $"ERROR: Recovery tool not found. Searched paths: {searchedPaths}";
            }

            //RichLogs($"Using recovery tool: {recoveryPath}", Color.LimeGreen, true);

            using (Process odinProcess = new Process())
            {
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = recoveryPath,
                    WorkingDirectory = Path.GetDirectoryName(recoveryPath) ?? Directory.GetCurrentDirectory(),
                    Arguments = cmdOptions,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };
                odinProcess.StartInfo = startInfo;
                string processOutput = "";
                string errorOutput = "";

                try
                {
                    if (flash)
                    {
                        int firmwareCounter = 0;
                        int progressPercent = 0;
                        odinProcess.OutputDataReceived += (object sender, DataReceivedEventArgs e) =>
                        {
                            ProcessFlashOutputData(e.Data, ref processOutput, ref firmwareCounter, ref progressPercent, checkMd5);
                        };

                        odinProcess.ErrorDataReceived += (object sender, DataReceivedEventArgs e) =>
                        {
                            if (!string.IsNullOrEmpty(e.Data))
                            {
                                errorOutput += e.Data + "\r\n";
                                RichLogs($"Error: {e.Data}", Color.IndianRed, true);
                            }
                        };
                    }
                    else
                    {
                        odinProcess.OutputDataReceived += (object sender, DataReceivedEventArgs e) =>
                        {
                            ProcessOutputData(e.Data, ref processOutput);
                        };

                        odinProcess.ErrorDataReceived += (object sender, DataReceivedEventArgs e) =>
                        {
                            if (!string.IsNullOrEmpty(e.Data))
                            {
                                errorOutput += e.Data + "\r\n";
                            }
                        };
                    }

                    odinProcess.Start();
                    odinProcess.BeginOutputReadLine();
                    odinProcess.BeginErrorReadLine();

                    Stopwatch stopwatch = new Stopwatch();
                    stopwatch.Start();

                    while (!odinProcess.WaitForExit(100))
                    {
                        if (stopwatch.ElapsedMilliseconds > timeout &&
                            !processOutput.Contains("Upload Binaries") &&
                            !processOutput.Contains("Check file :"))
                        {
                            RichLogs("Process timeout, terminating...", Color.Orange, true);
                            try { odinProcess.Kill(); } catch { }
                            break;
                        }
                    }

                    // Wait a bit more for any remaining output
                    odinProcess.WaitForExit(1000);

                    // Include error output in the result if present
                    if (!string.IsNullOrEmpty(errorOutput))
                    {
                        processOutput += "\r\nERROR OUTPUT:\r\n" + errorOutput;
                    }

                    // Check exit code
                    if (odinProcess.ExitCode != 0)
                    {
                        RichLogs($"Process exited with code: {odinProcess.ExitCode}", Color.Orange, true);
                        processOutput += $"\r\nEXIT CODE: {odinProcess.ExitCode}";
                    }

                    return processOutput;
                }
                catch (Exception ex)
                {
                    RichLogs($"Error executing recovery tool: {ex.Message}", Color.IndianRed, true);
                    return $"ERROR: {ex.Message}";
                }
            }
        }

        private void ProcessOutputData(string? data, ref string output)
        {
            if (!string.IsNullOrEmpty(data))
            {
                Console.WriteLine(data);
                output += data;

                // Process device info if needed
                ProcessDeviceInfo(data);
            }
        }

        private void ProcessFlashOutputData(string? data, ref string output, ref int firmwareCounter, ref int progressPercent, bool checkMd5 = false)
        {
            if (string.IsNullOrEmpty(data))
            {
                return;
            }

            // Track processed messages to avoid duplicates
            if (_reportedMessages.Contains(data.Trim()))
            {
                return;
            }

            // Add to reported messages
            _reportedMessages.Add(data.Trim());

            // Aggressively filter out any line that contains ONLY percentages
            if (data.Trim().Contains("%") && !data.Contains(".lz4") && !data.Contains("Upload Binaries") && 
                !data.Contains("Receive PIT Info") && !data.Contains("success getpit") && !data.Contains("Close Connection"))
            {
                // This is likely a percentage-only line, filter it out completely
                return;
            }

            // Handle different output patterns
            if (data.Contains("Check file :"))
            {
                // Skip MD5 check messages in clean mode unless explicitly requested
                if (!checkMd5)
                {
                    return;
                }
                string filePath = ExtractSubstring(data, "Check file : ", "");
                RichLogs("Checking file : ", Color.Silver, false);
                RichLogs(Path.GetFileName(filePath), Color.CornflowerBlue, true);
            }
            else if (data.Contains("Setup Connection"))
            {
                // Connection setup - already handled by initial status
                return;
            }
            else if (data.Contains("initializeConnection"))
            {
                // Connection initialization - already handled
                return;
            }
            else if (data.Contains("Set Partition"))
            {
                // Partition setup - already handled
                return;
            }
            else if (data.Contains("Erase..."))
            {
                // Erase operation - already handled
                return;
            }
            else if (data.Contains("Receive PIT Info"))
            {
                RichLogs("Receive PIT Info", Color.Silver, true);
            }
            else if (data.Contains("success getpit"))
            {
                RichLogs("success getpit", Color.LimeGreen, true);
            }
            else if (data.Contains("Upload Binaries"))
            {
                RichLogs("Upload Binaries", Color.Silver, true);
            }
            else if (data.Contains(".lz4"))
            {
                // Handle binary file uploads - detect all lz4 files with various extensions
                string fileName = data.Trim();
                
                // Aggressively remove ALL percentage content from the filename
                // Remove any percentage patterns like "25%", "50%", "75%", etc.
                var percentageRegex = new Regex(@"\s*\d+%\s*");
                fileName = percentageRegex.Replace(fileName, "");
                
                // Also remove any percentage in parentheses like "(25%)"
                var parenPercentageRegex = new Regex(@"\s*\(\d+%\)\s*");
                fileName = parenPercentageRegex.Replace(fileName, "");
                
                // Remove any trailing dots and spaces
                fileName = fileName.TrimEnd('.', ' ');
                
                if (!string.IsNullOrEmpty(fileName))
                {
                    // If we have a previous file, mark it as completed
                    if (!string.IsNullOrEmpty(_currentFlashingFile) && _currentFlashingFile != fileName)
                    {
                        RichLogs("Okay", Color.LimeGreen, true);
                    }
                    
                    RichLogs($"{fileName}... ", Color.Silver, false);
                    _currentFlashingFile = fileName;
                }
            }
            else if (data.Contains("%)"))
            {
                // Progress percentage - update progress but don't log to keep output clean
                try
                {
                    int currentProgress = int.Parse(ExtractSubstring(data, "(", "%)"));
                    if (currentProgress != progressPercent)
                    {
                        // Update progress
                        _currentProgress?.Report(new ProgressReport
                        {
                            CurrentFile = Path.GetFileName(_currentFlashingFile),
                            PercentComplete = currentProgress,
                            BytesTransferred = (flashTotalSize * currentProgress) / 100,
                            TotalBytes = flashTotalSize
                        });
                        progressPercent = currentProgress;
                    }
                }
                catch (FormatException)
                {
                    // Ignore invalid progress values
                }
                return; // Don't add to output to keep it clean
            }
            else if (data.Contains("(") && data.Contains("%)"))
            {
                // Handle cases where progress percentage is mixed with other text
                // Extract just the percentage part and update progress
                try
                {
                    string percentageText = ExtractSubstring(data, "(", "%)");
                    if (int.TryParse(percentageText, out int currentProgress))
                    {
                        if (currentProgress != progressPercent)
                        {
                            // Update progress
                            _currentProgress?.Report(new ProgressReport
                            {
                                CurrentFile = Path.GetFileName(_currentFlashingFile),
                                PercentComplete = currentProgress,
                                BytesTransferred = (flashTotalSize * currentProgress) / 100,
                                TotalBytes = flashTotalSize
                            });
                            progressPercent = currentProgress;
                        }
                    }
                }
                catch (FormatException)
                {
                    // Ignore invalid progress values
                }
                return; // Don't add to output to keep it clean
            }
            else if (data.Contains("%"))
            {
                // Catch ANY line containing percentages and filter them out
                try
                {
                    // Extract percentage for progress tracking only
                    string cleanData = data.Trim();
                    if (cleanData.Contains("%"))
                    {
                        // Use regex to find all percentage patterns
                        var regex = new Regex(@"(\d+)%");
                        var matches = regex.Matches(cleanData);
                        
                        foreach (Match match in matches)
                        {
                            if (int.TryParse(match.Groups[1].Value, out int currentProgress))
                            {
                                if (currentProgress != progressPercent)
                                {
                                    // Update progress
                                    _currentProgress?.Report(new ProgressReport
                                    {
                                        CurrentFile = Path.GetFileName(_currentFlashingFile),
                                        PercentComplete = currentProgress,
                                        BytesTransferred = (flashTotalSize * currentProgress) / 100,
                                        TotalBytes = flashTotalSize
                                    });
                                    progressPercent = currentProgress;
                                }
                            }
                        }
                    }
                }
                catch (FormatException)
                {
                    // Ignore invalid progress values
                }
                return; // Don't add to output to keep it clean
            }
            else if (data.Contains("Close Connection"))
            {
                // Mark the last file as completed
                if (!string.IsNullOrEmpty(_currentFlashingFile))
                {
                    RichLogs("Okay", Color.LimeGreen, true);
                    _currentFlashingFile = "";
                }
                RichLogs("Close Connection", Color.Silver, true);
            }
            else if (data.Contains("FAIL!") || data.Contains("ERROR") || data.Contains("Failed"))
            {
                // Mark current file as failed
                if (!string.IsNullOrEmpty(_currentFlashingFile))
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    _fileFailed[_currentFlashingFile] = true;
                    _currentFlashingFile = "";
                }
            }
            else if (data.Contains("COM") && !data.Contains("Check file"))
            {
                // COM port info - already handled
                return;
            }
            else if (!string.IsNullOrEmpty(data.Trim()) && 
                     !data.Trim().Contains("(") && 
                     !data.Trim().Contains(")") &&
                     !data.Contains("Upload Binaries") && 
                     !data.Contains("Receive PIT Info") &&
                     !data.Contains("success getpit") &&
                     !data.Contains("Close Connection") &&
                     !data.Contains("Setup Connection") &&
                     !data.Contains("initializeConnection") &&
                     !data.Contains("Set Partition") &&
                     !data.Contains("Erase..."))
            {
                // This might be a new file name or completion indicator
                string cleanData = data.Trim();
                
                // If we have a current file being processed and this looks like a new file
                if (!string.IsNullOrEmpty(_currentFlashingFile) && 
                    cleanData.Contains(".lz4"))
                {
                    // Mark previous file as completed
                    RichLogs("Okay", Color.LimeGreen, true);
                    _currentFlashingFile = "";
                }
            }

            // Add to output for debugging purposes
            output = output + data + "\r\n";
        }

        private void ExtractFirmware(string filePath, ref long totalSize, ref List<ObjectFirmwares> firmware)
        {
            // This method should extract firmware information from the file
            // You'll need to implement this based on your specific requirements
            // For now, adding basic file size calculation
            if (File.Exists(filePath))
            {
                FileInfo fileInfo = new FileInfo(filePath);
                totalSize += fileInfo.Length;

                firmware.Add(new ObjectFirmwares
                {
                    name = Path.GetFileName(filePath),
                    size = fileInfo.Length
                });
            }
        }

        private string ExtractSubstring(string input, string startDelimiter, string endDelimiter)
        {
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(startDelimiter))
                return string.Empty;

            int startIndex = input.IndexOf(startDelimiter);
            if (startIndex == -1)
                return string.Empty;

            startIndex += startDelimiter.Length;

            if (string.IsNullOrEmpty(endDelimiter))
                return input.Substring(startIndex);

            int endIndex = input.IndexOf(endDelimiter, startIndex);
            if (endIndex == -1)
                return input.Substring(startIndex);

            return input.Substring(startIndex, endIndex - startIndex);
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            // Clear collections
            _completedFiles?.Clear();
            _reportedMessages?.Clear();
            _deviceInfo?.Clear();
            _fileCompleted?.Clear();
            _fileFailed?.Clear();

            // Mark as disposed
            _disposed = true;
        }
    }
}