using System;
using System.Threading.Tasks;

namespace SamsungTool.Services.Interfaces
{
    public interface ILicenseService
    {
        Task<LicenseInfo?> GetLicenseInfoAsync();
    }

    public class LicenseInfo
    {
        public bool Status { get; set; }
        public string? Message { get; set; }
        public string? Date { get; set; }
        public string? Username { get; set; }
        public DateTime? ExpirationDate { get; set; }
    }
} 