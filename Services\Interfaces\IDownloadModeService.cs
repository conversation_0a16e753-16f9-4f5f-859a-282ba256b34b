﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Services.Interfaces
{
    public interface IDownloadModeService
    {
        Task ExecuteOperationAsync(string operation, CancellationToken cancellationToken = default);
        string? ReadInfoOdin(string port);
        Task ReadInfoMTKFRPAsync();
        Task<string> ExecuteRecoveryCommandAsync(string cmdOptions, CancellationToken cancellationToken = default);
    }
}