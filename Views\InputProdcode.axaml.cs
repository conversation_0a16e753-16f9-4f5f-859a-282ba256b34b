using Avalonia.Controls;
using Avalonia.Interactivity;
using System;
using System.Threading.Tasks;

namespace SamsungTool
{
    public partial class InputProdcode : Window
    {
        private TaskCompletionSource<string>? _taskCompletionSource;
        public string Result { get; private set; } = "";

        public InputProdcode()
        {
            InitializeComponent();
            this.Opened += OnWindowOpened;
        }

        private void OnWindowOpened(object? sender, EventArgs e)
        {
            txtProductCode.Focus();
        }

        public static async Task<string> ShowAsync(Window? parent)
        {
            var dialog = new InputProdcode();
            dialog._taskCompletionSource = new TaskCompletionSource<string>();

            try
            {
                if (parent != null)
                {
                    await dialog.ShowDialog(parent);
                }
                else
                {
                    dialog.Show();
                }

                return await dialog._taskCompletionSource.Task;
            }
            catch
            {
                return "";
            }
        }

        private void btnOK_Click(object? sender, RoutedEventArgs e)
        {
            var productCode = txtProductCode.Text?.Trim() ?? "";
            
            if (string.IsNullOrWhiteSpace(productCode))
            {
                ShowMessage("Please enter a product code.");
                return;
            }
            
            if (!productCode.StartsWith("SM-"))
            {
                ShowMessage("Product code should start with 'SM-'.");
                return;
            }
            
            if (productCode.Length < 10)
            {
                ShowMessage("Product code seems too short.");
                return;
            }

            Result = productCode;
            _taskCompletionSource?.SetResult(productCode);
            Close();
        }

        private void btnCancel_Click(object? sender, RoutedEventArgs e)
        {
            Result = "";
            _taskCompletionSource?.SetResult("");
            Close();
        }

        private void ShowMessage(string message)
        {
            try
            {
                // Simple approach - just focus back to textbox
                txtProductCode.Focus();
            }
            catch
            {
                // Ignore errors
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                if (_taskCompletionSource != null && !_taskCompletionSource.Task.IsCompleted)
                {
                    _taskCompletionSource.SetResult(Result);
                }
            }
            catch
            {
                // Ignore errors
            }
            
            base.OnClosed(e);
        }
    }
} 