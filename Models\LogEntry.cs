using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace SamsungTool.Models
{
    /// <summary>
    /// Represents a log entry for operation history
    /// </summary>
    public class LogEntry
    {
        /// <summary>
        /// Unique identifier for the log entry
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Timestamp when the operation started
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Timestamp when the operation completed
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Name of the operation (e.g., "Reading Information", "Remove FRP", etc.)
        /// </summary>
        public string OperationName { get; set; } = string.Empty;

        /// <summary>
        /// Type of operation (MTP, ADB, Download Mode, Flash)
        /// </summary>
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// Complete log content as rich text
        /// </summary>
        public string LogContent { get; set; } = string.Empty;

        /// <summary>
        /// Plain text version of log content for searching
        /// </summary>
        public string PlainTextContent { get; set; } = string.Empty;

        /// <summary>
        /// Status of the operation (Success, Failed, Cancelled)
        /// </summary>
        public LogEntryStatus Status { get; set; } = LogEntryStatus.InProgress;

        /// <summary>
        /// File path where the log is saved
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// Duration of the operation
        /// </summary>
        [JsonIgnore]
        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;

        /// <summary>
        /// Formatted duration string
        /// </summary>
        [JsonIgnore]
        public string DurationString
        {
            get
            {
                if (!Duration.HasValue) return "In Progress";
                var duration = Duration.Value;
                if (duration.TotalMinutes >= 1)
                    return $"{duration.Minutes}m {duration.Seconds}s";
                return $"{duration.Seconds}s";
            }
        }

        /// <summary>
        /// Formatted start time string
        /// </summary>
        [JsonIgnore]
        public string StartTimeString => StartTime.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// Status display string with color information
        /// </summary>
        [JsonIgnore]
        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    LogEntryStatus.Success => "✓ Completed",
                    LogEntryStatus.Failed => "✗ Failed",
                    LogEntryStatus.Cancelled => "⊘ Cancelled",
                    LogEntryStatus.InProgress => "⟳ In Progress",
                    _ => "Unknown"
                };
            }
        }

        /// <summary>
        /// Get status color for UI display
        /// </summary>
        [JsonIgnore]
        public System.Drawing.Color StatusColor
        {
            get
            {
                return Status switch
                {
                    LogEntryStatus.Success => System.Drawing.Color.LimeGreen,
                    LogEntryStatus.Failed => System.Drawing.Color.IndianRed,
                    LogEntryStatus.Cancelled => System.Drawing.Color.Orange,
                    LogEntryStatus.InProgress => System.Drawing.Color.CornflowerBlue,
                    _ => System.Drawing.Color.Silver
                };
            }
        }

        /// <summary>
        /// Create a summary of the log entry for display in history list
        /// </summary>
        [JsonIgnore]
        public string Summary
        {
            get
            {
                var summary = $"{StartTimeString} - {OperationName}";
                if (Duration.HasValue)
                    summary += $" ({DurationString})";
                return summary;
            }
        }
    }

    /// <summary>
    /// Status enumeration for log entries
    /// </summary>
    public enum LogEntryStatus
    {
        InProgress,
        Success,
        Failed,
        Cancelled
    }

    /// <summary>
    /// Collection of log entries with search and filter capabilities
    /// </summary>
    public class LogEntryCollection
    {
        public List<LogEntry> Entries { get; set; } = new List<LogEntry>();

        /// <summary>
        /// Add a new log entry
        /// </summary>
        public void AddEntry(LogEntry entry)
        {
            Entries.Add(entry);
        }

        /// <summary>
        /// Get entries filtered by operation type
        /// </summary>
        public List<LogEntry> GetByOperationType(string operationType)
        {
            return Entries.FindAll(e => e.OperationType.Equals(operationType, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Get entries filtered by status
        /// </summary>
        public List<LogEntry> GetByStatus(LogEntryStatus status)
        {
            return Entries.FindAll(e => e.Status == status);
        }

        /// <summary>
        /// Get entries from a specific date range
        /// </summary>
        public List<LogEntry> GetByDateRange(DateTime startDate, DateTime endDate)
        {
            return Entries.FindAll(e => e.StartTime >= startDate && e.StartTime <= endDate);
        }

        /// <summary>
        /// Search entries by content
        /// </summary>
        public List<LogEntry> SearchByContent(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new List<LogEntry>(Entries);

            return Entries.FindAll(e => 
                e.OperationName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                e.PlainTextContent.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Get recent entries (last N entries)
        /// </summary>
        public List<LogEntry> GetRecent(int count = 10)
        {
            var sortedEntries = new List<LogEntry>(Entries);
            sortedEntries.Sort((a, b) => b.StartTime.CompareTo(a.StartTime));
            return sortedEntries.Take(count).ToList();
        }
    }
}
