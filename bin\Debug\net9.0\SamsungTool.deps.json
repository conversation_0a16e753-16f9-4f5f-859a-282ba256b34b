{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"SamsungTool/*******": {"dependencies": {"Avalonia": "11.3.0", "Avalonia.Desktop": "11.3.0", "Avalonia.Diagnostics": "11.3.0", "Avalonia.Fonts.Inter": "11.3.0", "Avalonia.Themes.Fluent": "11.3.0", "CommunityToolkit.Mvvm": "8.4.0", "K4os.Compression.LZ4.Streams": "1.3.8", "Microsoft.DotNet.ILCompiler": "9.0.8", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.NET.ILLink.Tasks": "9.0.8", "Microsoft.Win32.Registry": "5.0.0", "QRCoder": "1.6.0", "System.Drawing.Common": "9.0.5", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.Ports": "9.0.5", "System.ServiceProcess.ServiceController": "9.0.5", "runtime.native.System.IO.Ports": "9.0.5"}, "runtime": {"SamsungTool.dll": {}}}, "Avalonia/11.3.0": {"dependencies": {"Avalonia.BuildServices": "0.0.31", "Avalonia.Remote.Protocol": "11.3.0", "MicroCom.Runtime": "0.11.0"}, "runtime": {"lib/net8.0/Avalonia.Base.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Controls.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.DesignerSupport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Avalonia.Dialogs.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Markup.Xaml.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Markup.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Metal.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.MicroCom.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.OpenGL.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Vulkan.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Angle.Windows.Natives/2.1.22045.20230930": {"runtimeTargets": {"runtimes/win-arm64/native/av_libglesv2.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "2.1.22045.0"}, "runtimes/win-x64/native/av_libglesv2.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.1.22045.0"}, "runtimes/win-x86/native/av_libglesv2.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2.1.22045.0"}}}, "Avalonia.BuildServices/0.0.31": {}, "Avalonia.Controls.ColorPicker/11.3.0": {"dependencies": {"Avalonia": "11.3.0", "Avalonia.Remote.Protocol": "11.3.0"}, "runtime": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Desktop/11.3.0": {"dependencies": {"Avalonia": "11.3.0", "Avalonia.Native": "11.3.0", "Avalonia.Skia": "11.3.0", "Avalonia.Win32": "11.3.0", "Avalonia.X11": "11.3.0"}, "runtime": {"lib/net8.0/Avalonia.Desktop.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Diagnostics/11.3.0": {"dependencies": {"Avalonia": "11.3.0", "Avalonia.Controls.ColorPicker": "11.3.0", "Avalonia.Themes.Simple": "11.3.0"}, "runtime": {"lib/net8.0/Avalonia.Diagnostics.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Fonts.Inter/11.3.0": {"dependencies": {"Avalonia": "11.3.0"}, "runtime": {"lib/net8.0/Avalonia.Fonts.Inter.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.FreeDesktop/11.3.0": {"dependencies": {"Avalonia": "11.3.0", "Tmds.DBus.Protocol": "0.21.2"}, "runtime": {"lib/net8.0/Avalonia.FreeDesktop.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Native/11.3.0": {"dependencies": {"Avalonia": "11.3.0"}, "runtime": {"lib/net8.0/Avalonia.Native.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "runtimeTargets": {"runtimes/osx/native/libAvaloniaNative.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Avalonia.Remote.Protocol/11.3.0": {"runtime": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Skia/11.3.0": {"dependencies": {"Avalonia": "11.3.0", "HarfBuzzSharp": "*******", "HarfBuzzSharp.NativeAssets.Linux": "*******", "HarfBuzzSharp.NativeAssets.WebAssembly": "*******", "SkiaSharp": "2.88.9", "SkiaSharp.NativeAssets.Linux": "2.88.9", "SkiaSharp.NativeAssets.WebAssembly": "2.88.9"}, "runtime": {"lib/net8.0/Avalonia.Skia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Themes.Fluent/11.3.0": {"dependencies": {"Avalonia": "11.3.0"}, "runtime": {"lib/net8.0/Avalonia.Themes.Fluent.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Themes.Simple/11.3.0": {"dependencies": {"Avalonia": "11.3.0"}, "runtime": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Win32/11.3.0": {"dependencies": {"Avalonia": "11.3.0", "Avalonia.Angle.Windows.Natives": "2.1.22045.20230930"}, "runtime": {"lib/net8.0/Avalonia.Win32.Automation.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Win32.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.X11/11.3.0": {"dependencies": {"Avalonia": "11.3.0", "Avalonia.FreeDesktop": "11.3.0", "Avalonia.Skia": "11.3.0"}, "runtime": {"lib/net8.0/Avalonia.X11.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "HarfBuzzSharp/*******": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "*******", "HarfBuzzSharp.NativeAssets.macOS": "*******"}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "*******"}}}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"dependencies": {"HarfBuzzSharp": "*******"}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.WebAssembly/*******": {}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "K4os.Compression.LZ4/1.3.8": {"runtime": {"lib/net6.0/K4os.Compression.LZ4.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net6.0/K4os.Compression.LZ4.Streams.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "K4os.Hash.xxHash/1.0.8": {"runtime": {"lib/net6.0/K4os.Hash.xxHash.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MicroCom.Runtime/0.11.0": {"runtime": {"lib/net5.0/MicroCom.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.DotNet.ILCompiler/9.0.8": {}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Primitives/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.NET.ILLink.Tasks/9.0.8": {}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "QRCoder/1.6.0": {"runtime": {"lib/net6.0/QRCoder.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Ports/9.0.5": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.5", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.5"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp/2.88.9": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.9.0"}}}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"dependencies": {"SkiaSharp": "2.88.9"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.WebAssembly/2.88.9": {}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.Buffers/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.EventLog/9.0.5": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/9.0.5": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.5"}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21601"}, "lib/net9.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21601"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines/8.0.0": {}, "System.IO.Ports/9.0.5": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.5"}, "runtime": {"lib/net9.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}, "runtimes/win/lib/net9.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceProcess.ServiceController/9.0.5": {"dependencies": {"System.Diagnostics.EventLog": "9.0.5"}, "runtime": {"lib/net9.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Tmds.DBus.Protocol/0.21.2": {"dependencies": {"System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Tmds.DBus.Protocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}}}, "libraries": {"SamsungTool/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Avalonia/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/j8J0QcekWFBl+DE/4g8WeSGwUK11auhh5d1o5pfOgi5gMrG6V9QdTsPv0ZCDL73c7dJwwL1BZn1WrhE11I76g==", "path": "avalonia/11.3.0", "hashPath": "avalonia.11.3.0.nupkg.sha512"}, "Avalonia.Angle.Windows.Natives/2.1.22045.20230930": {"type": "package", "serviceable": true, "sha512": "sha512-Bo3qOhKC1b84BIhiogndMdAzB3UrrESKK7hS769f5HWeoMw/pcd42US5KFYW2JJ4ZSTrXnP8mXwLTMzh+S+9Lg==", "path": "avalonia.angle.windows.natives/2.1.22045.20230930", "hashPath": "avalonia.angle.windows.natives.2.1.22045.20230930.nupkg.sha512"}, "Avalonia.BuildServices/0.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-KmCN6Hc+45q4OnF10ge450yVUvWuxU6bdQiyKqiSvrHKpahNrEdk0kG6Ip6GHk2SKOCttGQuA206JVdkldEENg==", "path": "avalonia.buildservices/0.0.31", "hashPath": "avalonia.buildservices.0.0.31.nupkg.sha512"}, "Avalonia.Controls.ColorPicker/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MqwZPVdBiDc0h6DHVG5Igb+bd+3o7IOM9nbHgNPlJn38xG0jQb+YqqRcvnt/KnD5FBEaXZcGmiW3rqU9ydXvlg==", "path": "avalonia.controls.colorpicker/11.3.0", "hashPath": "avalonia.controls.colorpicker.11.3.0.nupkg.sha512"}, "Avalonia.Desktop/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X1nUSIDHPFt34U8Dn4Hk8/c/i+rN3pA0Nv4awfYHlYdenmLqE82gm8fpKmQE6/OfguUN1yMYi/uU9lAAAbd2hg==", "path": "avalonia.desktop/11.3.0", "hashPath": "avalonia.desktop.11.3.0.nupkg.sha512"}, "Avalonia.Diagnostics/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P152VhCexkrvTLRiRj7mSzUSMGKwTEsbz/XWTogV/33vGVWeCC8tStf/c8YsRrU5kSITtKeCV52UOh4m46hTfA==", "path": "avalonia.diagnostics/11.3.0", "hashPath": "avalonia.diagnostics.11.3.0.nupkg.sha512"}, "Avalonia.Fonts.Inter/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PXi6Ehk5LwZBBkgSXmxsB5iW4UVSLyS3t39it1uB5bGIhagiDMh1RK4kuiZive7f6NRDfRSDCmpsqRKNDGvLg==", "path": "avalonia.fonts.inter/11.3.0", "hashPath": "avalonia.fonts.inter.11.3.0.nupkg.sha512"}, "Avalonia.FreeDesktop/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NUoXEEmZ4gbXn5c2Smv2QfDT23Ps2x2O1Dls5/op1AjmxZFXRkx/zz1xQVnJu6cHEA2Jx4i+SG7AvCPVs8bBBA==", "path": "avalonia.freedesktop/11.3.0", "hashPath": "avalonia.freedesktop.11.3.0.nupkg.sha512"}, "Avalonia.Native/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1KFRYgxY9IVxceXB+/jRH0zCrhcKDOs/mTxLexZGd5MT8oKs0NTnAHP8R09bCQOw9aGcYLpzXVzKzhpGulxxWQ==", "path": "avalonia.native/11.3.0", "hashPath": "avalonia.native.11.3.0.nupkg.sha512"}, "Avalonia.Remote.Protocol/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-noH0+YgFsXshhDnEcBATw5EULNiTYv4gznE2OnSPDvU5K9/COVs6W3ZWlYY1lwY+w4x1IGueMux7dcw0TbjEzA==", "path": "avalonia.remote.protocol/11.3.0", "hashPath": "avalonia.remote.protocol.11.3.0.nupkg.sha512"}, "Avalonia.Skia/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6R3+Qp3u0W70MFAxBD4bjRVwQtXs7TstsD2eqLR1HOJI07sha2dh2GoogOI5hKLBn32fD6gek7iSJL6kqH5OiA==", "path": "avalonia.skia/11.3.0", "hashPath": "avalonia.skia.11.3.0.nupkg.sha512"}, "Avalonia.Themes.Fluent/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRcR0xKhpnHikIhtiE1vchuNHD/hvqLtto9yKmw73eZyhsyQMCncCu3GGfWTCCHOxJfawdMUCfUWo8COqmgLZw==", "path": "avalonia.themes.fluent/11.3.0", "hashPath": "avalonia.themes.fluent.11.3.0.nupkg.sha512"}, "Avalonia.Themes.Simple/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-pGurEpish9NS89iiBLbvIlN+DJ5hGaW1ABxcIl6XL9M/WcvUPtLznxoI0dbW2zw8OLDSsvowbsvQLdyknEvmhA==", "path": "avalonia.themes.simple/11.3.0", "hashPath": "avalonia.themes.simple.11.3.0.nupkg.sha512"}, "Avalonia.Win32/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-25gOjtAyWTEMCfOGioJEZ1iOH2jdOTnVpyK4y8ku1n4CKwnsml9XFbQum253y5rFlty29Vs0TiYzsC0CWuWAVg==", "path": "avalonia.win32/11.3.0", "hashPath": "avalonia.win32.11.3.0.nupkg.sha512"}, "Avalonia.X11/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BFkNmxW0pyqrg7q62tAEv7ZCfkR4k2bz39B3umU2VLRrYpsH/b2DocXeqgeYZYJbHikSPAXLkY34IHLZtdkU1w==", "path": "avalonia.x11/11.3.0", "hashPath": "avalonia.x11.11.3.0.nupkg.sha512"}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "HarfBuzzSharp/*******": {"type": "package", "serviceable": true, "sha512": "sha512-Hq+5+gx10coOvuRgB13KBwiWxJq1QeYuhtVLbA01ZCWaugOnolUahF44KvrQTUUHDNk/C7HB6SMaebsZeOdhgg==", "path": "harfbuzzsharp/*******", "hashPath": "harfbuzzsharp.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"type": "package", "serviceable": true, "sha512": "sha512-hkcHeTfOyIeJuPtO/QfoqkDvV/MXebZYaA/Bn/S+nXsjH3Wt9oQ6okH2kklYO+1UUdBSJFd67bi9IrpQXI2mPw==", "path": "harfbuzzsharp.nativeassets.linux/*******", "hashPath": "harfbuzzsharp.nativeassets.linux.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"type": "package", "serviceable": true, "sha512": "sha512-UAwIYnkbBTzBJv1Id8FijY/i8QiIepRemSXufU8fyzwWhYJdx4+ajG8yQUie5HW/uusbVLFSr26muSlJOFDgSw==", "path": "harfbuzzsharp.nativeassets.macos/*******", "hashPath": "harfbuzzsharp.nativeassets.macos.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.WebAssembly/*******": {"type": "package", "serviceable": true, "sha512": "sha512-OpheDNp9a3nC6hWNACemWkNEXJ4tWP3Gw9bykw3FbyeEmU2nUDtLIp6VgNnjHAPRMgUs1Kl7m4gJpzVYwC7CZw==", "path": "harfbuzzsharp.nativeassets.webassembly/*******", "hashPath": "harfbuzzsharp.nativeassets.webassembly.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"type": "package", "serviceable": true, "sha512": "sha512-RPxRXD16KtSs8Yxr2RK9Qs7AwyN9MlpqZIYs0AvfaJwl7RAtVhC0+u2f2SKwX0uMYYd3O98Z+OBA1sj6aWVKQA==", "path": "harfbuzzsharp.nativeassets.win32/*******", "hashPath": "harfbuzzsharp.nativeassets.win32.*******.nupkg.sha512"}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-LhwlPa7c1zs1OV2XadMtAWdImjLIsqFJPoRcIWAadSRn0Ri1DepK65UbWLPmt4riLqx2d40xjXRk0ogpqNtK7g==", "path": "k4os.compression.lz4/1.3.8", "hashPath": "k4os.compression.lz4.1.3.8.nupkg.sha512"}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-P15qr8dZAeo9GvYbUIPEYFQ0MEJ0i5iqr37wsYeRC3la2uCldOoeCa6to0CZ1taiwxIV+Mk8NGuZi+4iWivK9w==", "path": "k4os.compression.lz4.streams/1.3.8", "hashPath": "k4os.compression.lz4.streams.1.3.8.nupkg.sha512"}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Wp2F7BamQ2Q/7Hk834nV9vRQapgcr8kgv9Jvfm8J3D0IhDqZMMl+a2yxUq5ltJitvXvQfB8W6K4F4fCbw/P6YQ==", "path": "k4os.hash.xxhash/1.0.8", "hashPath": "k4os.hash.xxhash.1.0.8.nupkg.sha512"}, "MicroCom.Runtime/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "path": "microcom.runtime/0.11.0", "hashPath": "microcom.runtime.0.11.0.nupkg.sha512"}, "Microsoft.DotNet.ILCompiler/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-cMwVh5hsxAhv+oMHQdgcXodt2kDpfviofBO4IXupSAHJW2vZOZOOIhvPWRGO6NeGcP8SR4OpSwktRqb0i79KFA==", "path": "microsoft.dotnet.ilcompiler/9.0.8", "hashPath": "microsoft.dotnet.ilcompiler.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "path": "microsoft.extensions.logging/9.0.5", "hashPath": "microsoft.extensions.logging.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "path": "microsoft.extensions.options/9.0.5", "hashPath": "microsoft.extensions.options.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "path": "microsoft.extensions.primitives/9.0.5", "hashPath": "microsoft.extensions.primitives.9.0.5.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-rd1CbIsMtVPtZNTIVD6Xydue//klYOOQIDpRgu3BHtv17AlpRs74/6QFbcYgMm/jL+naVU2T3OFLxVSLV5lQLQ==", "path": "microsoft.net.illink.tasks/9.0.8", "hashPath": "microsoft.net.illink.tasks.9.0.8.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-D4OYNpmvAsF9MkaY2W8Jue2XuNHDhygvwzo019hs+lP85KaVnOlXmqsjDKr1dHb1DPxDnOKpe6mAgJN7S6ttwg==", "path": "microsoft.win32.systemevents/9.0.5", "hashPath": "microsoft.win32.systemevents.9.0.5.nupkg.sha512"}, "QRCoder/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "path": "qrcoder/1.6.0", "hashPath": "qrcoder.1.6.0.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-jdX92XbJ6rBTSPb3VwYRxDs6s5Vj9KcVDP6++6pVNF/D7OI13hgklUSVGMBRELpiC7SDEp/Bpsu980liACzC3g==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-z1LP33lTie6vWZR6dUXip5hOTobZ1WuXO+xpPCRcaKZo0bNkmEGaDatrwKnEUYxm/3PFBgFkH+M/s7uEub0SKQ==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-7xLefsg9LjqPIIhlFjVmJ41rIygrUxCvmOlrdWBoCJZLCK1uii7OovtX1iPwDe3I1Z1zZeEAPsMMN38Q5kJjPw==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-p20gfW2eN8CvUBwkrkHJGvY4+6vERGqmGiYmViv2of29MgzkWYdrOM7BaFIRPtmN0zNp1QGGIgmXl1W1cti//Q==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Psp2JYBZ6CjEBew2qioj7zrSfwXjujPuuh8fuKnfMfkABP9F55pMT3UNfZen7gKVUoiPNhvAj0DrwUY0lKtmxA==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rv3UX69IGI6VLM55OIYvoqbeAsicoit5gfk5T8VYy54B3lOIejwYa2QFcut9MW5cjxMH+dFCZ658fMd9jL+faA==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-8r4n05KMOeBkIiTyLP0jYCPwUVJ40RMEEUBTN/rrS+0X+Pj00zd8Qw48VW7JJ3PS7RFeJLiAP8q7uPCnAiFYPg==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-1hcUPsq3RkJFp/4EYmhiS6NrjvpbaiqIUWYrn3gCs/pJIzEPB52GyyeeeabRrz720PkewEwUWtCXghCCtXrNqA==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-jRLQt8O72nEtSelkrY8KL4Tj9ojbXl6zSQhmZ0NAQB1yZmihcFfqaHeqckreyUJ2xET/vb+tXZy1mcDHKop6Bw==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-1yN18dV+F0Y/dMaAWCJ3eYVrBOi1j9u4TDA//oX5FqVjCHkYm8wG718EDXLhUYe9+JNH7ijFDl/+Ac2WAz2p+A==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-l13ArjVyln6Zp4dHNXQDv38wWI6uT6LMiAygcQuYYU7fuEwM33WPBD7yNIaZEM2u5y+80u10p9TijIKHzDndrw==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Hwr1s9Yxv07IFKUnR04ZVnUvukGCbpCtB0Lu3tfrRTEChLU8aCICCd6nPu37HBVNv7Cmpl78zLaFUHM7MA6EBw==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ML0BtpUQ39NhTM1PWihbtPpY1gZraLTXd14oyx46KmCWi0se8Xw3ph0zonvp3NXr86kAhvXwSnOIQqmZ+GMuXA==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-nYwoFTuKG1i48QHaGI3xoXRtWhMdvcwaP/zgnrtaLpDYlF3K+XPDPsMZdILU5EOkqFu9WPYhcP3Lf78yUOCFIg==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-VBaRoAdPdpwvFf8r/A9Ob3GEuXebdqBCw339+UbLeSZZZJf4Rn0vFSZv078G85MBwLLGkSOURKAjljsa7EOmRA==", "path": "runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ibaf8gfeFmlzHDFt6wHimINiKm60gtd7lQmx1YzVUZyp75z3He1bPDl/eNM4o0iMe//gYftrnFrEQGEVYPnq4Q==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-IzJV9kWskpB4KJbVxixUR404Sp/ABNXL3tmqWhb7nLSvrU7PZ3Hm7c9ycX0MRY4WmGkrFLICisDq1dmL9cxooA==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "SkiaSharp/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "path": "skiasharp/2.88.9", "hashPath": "skiasharp.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-cWSaJKVPWAaT/WIn9c8T5uT/l4ETwHxNJTkEOtNKjphNo8AW6TF9O32aRkxqw3l8GUdUo66Bu7EiqtFh/XG0Zg==", "path": "skiasharp.nativeassets.linux/2.88.9", "hashPath": "skiasharp.nativeassets.linux.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA==", "path": "skiasharp.nativeassets.macos/2.88.9", "hashPath": "skiasharp.nativeassets.macos.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.WebAssembly/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-kt06RccBHSnAs2wDYdBSfsjIDbY3EpsOVqnlDgKdgvyuRA8ZFDaHRdWNx1VHjGgYzmnFCGiTJBnXFl5BqGwGnA==", "path": "skiasharp.nativeassets.webassembly/2.88.9", "hashPath": "skiasharp.nativeassets.webassembly.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w==", "path": "skiasharp.nativeassets.win32/2.88.9", "hashPath": "skiasharp.nativeassets.win32.2.88.9.nupkg.sha512"}, "System.Buffers/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ratu44uTIHgeBeI0dE8DWvmXVBSo4u7ozRZZHOMmK/JPpYyo0dAfgSiHlpiObMQ5lEtEyIXA40sKRYg5J6A8uQ==", "path": "system.buffers/4.3.0", "hashPath": "system.buffers.4.3.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-nhtTvAgKTD7f6t0bkOb4/hNv0PShb8GHs5Fhn7PvYhwhyWiVyVBvL2vTGH0Hlw5jOZQmWkzQxjY6M/h4tl8M6Q==", "path": "system.diagnostics.eventlog/9.0.5", "hashPath": "system.diagnostics.eventlog.9.0.5.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-T/6nqx0B7/uTe5JjBwrKZilLuwfhHLOVmNKlT/wr4A9Dna94mgTdz3lTfrdJ72QRx7IHCv/LzoJPmFSfK/N6WA==", "path": "system.drawing.common/9.0.5", "hashPath": "system.drawing.common.9.0.5.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ni0EW9jqQVgnx+v5XH/TFbahQF2lRXLQRe4OYFwxuMLLTOkkdOO8zGV3DiG18tCCo2ojpRqBaDGkt0MADQ4ITw==", "path": "system.io.ports/9.0.5", "hashPath": "system.io.ports.9.0.5.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceProcess.ServiceController/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-3mOK5BIwcBHAWzrH9oHCEgwmHecIgoW/P0B42MB8UgG3TqH5K68MBt1/4Mn7znexNP2o6AniDJIXfg04+feELA==", "path": "system.serviceprocess.servicecontroller/9.0.5", "hashPath": "system.serviceprocess.servicecontroller.9.0.5.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "Tmds.DBus.Protocol/0.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-ScSMrUrrw8px4kK1Glh0fZv/HQUlg1078bNXNPfRPKQ3WbRzV9HpsydYEOgSoMK5LWICMf2bMwIFH0pGjxjcMA==", "path": "tmds.dbus.protocol/0.21.2", "hashPath": "tmds.dbus.protocol.0.21.2.nupkg.sha512"}}}