﻿using SamsungTool.Library;
using SamsungTool.Library.GUI;
using SamsungTool.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations
{
    public class FlashService : IFlashService, IDisposable
    {
        private static readonly OdinService _odin = new();
        private bool _disposed = false;

        public async Task StartFlashAsync(IProgress<ProgressReport> progress, CancellationToken cancellationToken = default)
        {
            RichLogs("Operation Flash firmware ", Color.Silver, true);
            RichLogs("Waiting for device... ", Color.Silver, false);

            string? Portname = null;

            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                if (MainWindow.GUI?.cmbCOM.SelectedItem == null)
                {
                    RichLogs("No Device selected!", Color.IndianRed, true);
                    return;
                }

                if (MainWindow.GUI?.cmbCOM.SelectedItem is USB.COMINFO comInfo)
                {
                    Portname = comInfo.Name;
                }
                else if (MainWindow.GUI?.cmbCOM.SelectedItem is string selectedText)
                {
                    var match = Regex.Match(selectedText, @"\((COM\d+)\)");
                    if (match.Success)
                    {
                        Portname = match.Groups[1].Value;
                    }
                }
            });

            cancellationToken.ThrowIfCancellationRequested();

            if (string.IsNullOrEmpty(Portname))
            {
                RichLogs("Failed!", Color.IndianRed, true);
                return;
            }

            RichLogs($"{Portname}", Color.CornflowerBlue, true);

            var flashEntries = new List<FlashEntry>();

            if (MainWindow.GUI?.cbFlashBL.IsChecked.GetValueOrDefault() == true && !string.IsNullOrEmpty(MainWindow.GUI?.txtFlashBL.Text))
            {
                flashEntries.Add(new FlashEntry { Type = PartitionType.BL, FilePath = MainWindow.GUI?.txtFlashBL.Text });
                RichLogs($"BL file: {Path.GetFileName(MainWindow.GUI?.txtFlashBL.Text ?? "")}", Color.CornflowerBlue, true);
            }

            if (MainWindow.GUI?.cbFlashAP.IsChecked.GetValueOrDefault() == true && !string.IsNullOrEmpty(MainWindow.GUI?.txtFlashAP.Text))
            {
                flashEntries.Add(new FlashEntry { Type = PartitionType.AP, FilePath = MainWindow.GUI?.txtFlashAP.Text });
                RichLogs($"AP file: {Path.GetFileName(MainWindow.GUI?.txtFlashAP.Text ?? "")}", Color.CornflowerBlue, true);
            }

            if (MainWindow.GUI?.cbFlashCP.IsChecked.GetValueOrDefault() == true && !string.IsNullOrEmpty(MainWindow.GUI?.txtFlashCP.Text))
            {
                flashEntries.Add(new FlashEntry { Type = PartitionType.CP, FilePath = MainWindow.GUI?.txtFlashCP.Text });
                RichLogs($"CP file: {Path.GetFileName(MainWindow.GUI?.txtFlashCP.Text ?? "")}", Color.CornflowerBlue, true);
            }

            if (MainWindow.GUI?.cbFlashCSC.IsChecked.GetValueOrDefault() == true && !string.IsNullOrEmpty(MainWindow.GUI?.txtFlashCSC.Text))
            {
                flashEntries.Add(new FlashEntry { Type = PartitionType.CSC, FilePath = MainWindow.GUI?.txtFlashCSC.Text });
                RichLogs($"CSC file: {Path.GetFileName(MainWindow.GUI?.txtFlashCSC.Text ?? "")}", Color.CornflowerBlue, true);
            }

            if (MainWindow.GUI?.cbFlashDATA.IsChecked.GetValueOrDefault() == true && !string.IsNullOrEmpty(MainWindow.GUI?.txtFlashDATA.Text))
            {
                flashEntries.Add(new FlashEntry { Type = PartitionType.USERDATA, FilePath = MainWindow.GUI?.txtFlashDATA.Text });
                RichLogs($"USERDATA file: {Path.GetFileName(MainWindow.GUI?.txtFlashDATA.Text ?? "")}", Color.CornflowerBlue, true);
            }

            if (!flashEntries.Any())
            {
                RichLogs("No firmware selected!", Color.IndianRed, true);
                return;
            }

            var options = new FlashOptions
            {
                AutoReboot = MainWindow.GUI?.ckAutoReboot.IsChecked.GetValueOrDefault() ?? false,
                ValidateMd5 = MainWindow.GUI?.CkMD5.IsChecked.GetValueOrDefault() ?? false,
                NandErase = MainWindow.GUI?.CkNandErase.IsChecked.GetValueOrDefault() ?? false
            };

            RichLogs("", Color.Silver, true);
            RichLogs("Flash options:", Color.Silver, true);
            RichLogs($"Auto Reboot: {(options.AutoReboot ? "Enabled" : "Disabled")}", Color.CornflowerBlue, true);
            RichLogs($"Validate MD5: {(options.ValidateMd5 ? "Enabled" : "Disabled")}", Color.CornflowerBlue, true);
            RichLogs($"NAND Erase: {(options.NandErase ? "Enabled" : "Disabled")}", Color.CornflowerBlue, true);
            RichLogs("", Color.Silver, true);

            try
            {
                await _odin.FlashAsync(Portname, options, flashEntries, progress, cancellationToken);
            }
            catch (Exception ex)
            {
                RichLogs("", Color.Silver, true);
                RichLogs($"Flash failed: {ex.Message}", Color.IndianRed, true);
                throw;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
            }

            _disposed = true;
        }

        ~FlashService()
        {
            Dispose(false);
        }
    }
}