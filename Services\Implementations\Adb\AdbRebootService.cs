﻿using System;
using System.Threading;
using System.Threading.Tasks;
using SamsungTool.Library;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbRebootService : AdbServiceBase
    {
        public async Task RunDownloadMode(CancellationToken cancellationToken = default)
        {
            // Use the new common operations utility for standard initialization
            bool initialized = await AdbCommonOperations.ExecuteStandardInitializationAsync(
                InitializeAsync,
                FindDeviceAsync,
                ReadDeviceInfoAsync,
                cancellationToken);

            if (!initialized)
            {
                return;
            }

            // Execute reboot to download mode using the common utility
            await AdbCommonOperations.ExecuteStandardRebootAsync(ADB, "download", cancellationToken);

            // Use the new logging utility for operation completion
            AdbCommonOperations.LogOperationCompleted();
        }

        public async Task RunRecoveryMode(CancellationToken cancellationToken = default)
        {
            // Use the new common operations utility for standard initialization
            bool initialized = await AdbCommonOperations.ExecuteStandardInitializationAsync(
                InitializeAsync,
                FindDeviceAsync,
                ReadDeviceInfoAsync,
                cancellationToken);

            if (!initialized)
            {
                return;
            }

            // Execute reboot to recovery mode using the common utility
            await AdbCommonOperations.ExecuteStandardRebootAsync(ADB, "recovery", cancellationToken);

            // Use the new logging utility for operation completion
            AdbCommonOperations.LogOperationCompleted();
        }


    }
}