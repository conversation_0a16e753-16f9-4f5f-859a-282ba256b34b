using System;
using System.Threading;
using System.Threading.Tasks;
using SamsungTool.Library;

namespace SamsungTool.Services
{
    /// <summary>
    /// Base class for all services providing common functionality
    /// Maintains AOT compatibility and preserves existing error handling patterns
    /// </summary>
    public abstract class ServiceBase : IDisposable
    {
        protected bool _disposed = false;
        private readonly object _disposeLock = new object();

        /// <summary>
        /// Executes an operation with standard error handling and logging
        /// </summary>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="operation">The operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        protected async Task ExecuteOperationAsync(
            string operationName,
            Func<CancellationToken, Task> operation,
            CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await operation(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                ServiceLoggingUtility.LogOperationCancelled(operationName);
                throw; // Re-throw to maintain existing behavior
            }
            catch (Exception ex)
            {
                ServiceLoggingUtility.LogException(operationName, ex);
                throw; // Re-throw to maintain existing error handling behavior
            }
        }

        /// <summary>
        /// Executes an operation with standard error handling and returns result
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="operation">The operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        protected async Task<T> ExecuteOperationAsync<T>(
            string operationName,
            Func<CancellationToken, Task<T>> operation,
            CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                return await operation(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                ServiceLoggingUtility.LogOperationCancelled(operationName);
                throw; // Re-throw to maintain existing behavior
            }
            catch (Exception ex)
            {
                ServiceLoggingUtility.LogException(operationName, ex);
                throw; // Re-throw to maintain existing error handling behavior
            }
        }

        /// <summary>
        /// Executes an operation with retry logic
        /// </summary>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="operation">The operation to execute</param>
        /// <param name="maxRetries">Maximum number of retries</param>
        /// <param name="retryDelay">Delay between retries in milliseconds</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        protected async Task ExecuteWithRetryAsync(
            string operationName,
            Func<CancellationToken, Task> operation,
            int maxRetries = 3,
            int retryDelay = 1000,
            CancellationToken cancellationToken = default)
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    await operation(cancellationToken);
                    return; // Success, exit retry loop
                }
                catch (OperationCanceledException)
                {
                    throw; // Don't retry on cancellation
                }
                catch (Exception ex)
                {
                    if (attempt == maxRetries)
                    {
                        ServiceLoggingUtility.LogException(operationName, ex);
                        throw; // Last attempt failed, re-throw
                    }
                    else
                    {
                        ServiceLoggingUtility.LogRetryAttempt(attempt, maxRetries, operationName);
                        await Task.Delay(retryDelay, cancellationToken);
                    }
                }
            }
        }

        /// <summary>
        /// Executes an operation with retry logic and returns result
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="operation">The operation to execute</param>
        /// <param name="maxRetries">Maximum number of retries</param>
        /// <param name="retryDelay">Delay between retries in milliseconds</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        protected async Task<T> ExecuteWithRetryAsync<T>(
            string operationName,
            Func<CancellationToken, Task<T>> operation,
            int maxRetries = 3,
            int retryDelay = 1000,
            CancellationToken cancellationToken = default)
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    return await operation(cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    throw; // Don't retry on cancellation
                }
                catch (Exception ex)
                {
                    if (attempt == maxRetries)
                    {
                        ServiceLoggingUtility.LogException(operationName, ex);
                        throw; // Last attempt failed, re-throw
                    }
                    else
                    {
                        ServiceLoggingUtility.LogRetryAttempt(attempt, maxRetries, operationName);
                        await Task.Delay(retryDelay, cancellationToken);
                    }
                }
            }

            // This should never be reached, but compiler requires it
            throw new InvalidOperationException("Retry loop completed without success or exception");
        }

        /// <summary>
        /// Logs the start of a service operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="clearLog">Whether to clear the log first</param>
        protected void LogOperationStart(string operationName, bool clearLog = false)
        {
            ServiceLoggingUtility.LogOperationStart(operationName, clearLog);
        }

        /// <summary>
        /// Logs the completion of a service operation
        /// </summary>
        /// <param name="customMessage">Custom completion message</param>
        protected void LogOperationCompleted(string customMessage = "Operation Completed")
        {
            ServiceLoggingUtility.LogOperationCompleted(customMessage);
        }

        /// <summary>
        /// Validates that the service is not disposed
        /// </summary>
        /// <exception cref="ObjectDisposedException">Thrown if service is disposed</exception>
        protected void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(GetType().Name);
            }
        }

        /// <summary>
        /// Disposes the service
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the service with option to dispose managed resources
        /// </summary>
        /// <param name="disposing">True to dispose managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            lock (_disposeLock)
            {
                if (!_disposed)
                {
                    if (disposing)
                    {
                        // Dispose managed resources in derived classes
                        DisposeManagedResources();
                    }

                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// Override this method in derived classes to dispose managed resources
        /// </summary>
        protected virtual void DisposeManagedResources()
        {
            // Default implementation does nothing
            // Derived classes should override this to dispose their specific resources
        }

        /// <summary>
        /// Finalizer
        /// </summary>
        ~ServiceBase()
        {
            Dispose(false);
        }
    }
}
