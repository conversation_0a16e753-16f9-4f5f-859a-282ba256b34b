<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="SamsungTool.InputProdcode"
        Title="Change Product Color Code"
        Width="480" Height="380"
        MinWidth="450" MinHeight="350"
        WindowStartupLocation="CenterOwner"
        Background="#1E1E1E"
        CanResize="False"
        SystemDecorations="Full"
        Icon="avares://SamsungTool/Resources/logo.ico">

    <Window.Styles>
        <Style Selector="TextBox#txtProductCode">
            <Setter Property="Background" Value="#1A1A1A"/>
            <Setter Property="Foreground" Value="#FFFFFF"/>
            <Setter Property="BorderBrush" Value="#3F3F3F"/>
            <Setter Property="SelectionBrush" Value="#A83232"/>
            <Setter Property="SelectionForegroundBrush" Value="#FFFFFF"/>
        </Style>
        <Style Selector="TextBox#txtProductCode:focus">
            <Setter Property="Background" Value="#1A1A1A"/>
            <Setter Property="BorderBrush" Value="#A83232"/>
        </Style>
        <Style Selector="TextBox#txtProductCode:pointerover">
            <Setter Property="Background" Value="#1A1A1A"/>
        </Style>
        <Style Selector="TextBox#txtProductCode /template/ Border">
            <Setter Property="Background" Value="#1A1A1A"/>
        </Style>
        <Style Selector="TextBox#txtProductCode:focus /template/ Border">
            <Setter Property="Background" Value="#1A1A1A"/>
        </Style>
        <Style Selector="TextBox#txtProductCode /template/ ScrollViewer">
            <Setter Property="Background" Value="#1A1A1A"/>
        </Style>
        <Style Selector="TextBox#txtProductCode /template/ TextPresenter">
            <Setter Property="Background" Value="#1A1A1A"/>
        </Style>

        <Style Selector="Button">
            <Setter Property="Background" Value="#333333"/>
            <Setter Property="Foreground" Value="#E8E8E8"/>
            <Setter Property="BorderBrush" Value="#505050"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <Style Selector="Button:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#404040"/>
        </Style>

        <Style Selector="Button.accent">
            <Setter Property="Background" Value="#A83232"/>
            <Setter Property="Foreground" Value="#FFFFFF"/>
            <Setter Property="BorderBrush" Value="#A83232"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <Style Selector="Button.accent:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#B83838"/>
        </Style>
    </Window.Styles>

    <Grid RowDefinitions="Auto,*,Auto">
        <!-- Header -->
        <Border Grid.Row="0"
                Background="#252525"
                BorderBrush="#3F3F3F"
                BorderThickness="0,0,0,1"
                Padding="16,12">
            <TextBlock Text="Change Product Color Code"
                       FontWeight="SemiBold"
                       FontSize="15"
                       Foreground="#F0F0F0"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="16" VerticalScrollBarVisibility="Auto">
            <StackPanel Spacing="14">
                <!-- Input Section -->
                <StackPanel Spacing="8">
                    <TextBlock Text="Enter New Product Code:"
                               FontSize="13"
                               Foreground="#E8E8E8"
                               FontWeight="Medium"/>
                    <TextBox x:Name="txtProductCode" 
                             Watermark="e.g., SM-S928BZTCMEA"
                             Height="30"
                             Background="#1A1A1A"
                             Foreground="#FFFFFF"
                             BorderBrush="#3F3F3F"
                             BorderThickness="1"
                             CornerRadius="3"
                             Padding="8,5"
                             FontSize="13"
                             VerticalContentAlignment="Center"/>
                </StackPanel>

                <!-- Examples Section -->
                <StackPanel Spacing="8">
                    <TextBlock Text="Examples:"
                               FontSize="13"
                               Foreground="#E8E8E8"
                               FontWeight="Medium"/>
                    <StackPanel Spacing="4">
                        <TextBlock Text="• SM-S928BZTCMEA (Galaxy S24 Ultra Titanium Gray)" 
                                   FontFamily="Consolas" FontSize="12" Foreground="#D0D0D0"/>
                        <TextBlock Text="• SM-S928BZVCMEA (Galaxy S24 Ultra Titanium Violet)" 
                                   FontFamily="Consolas" FontSize="12" Foreground="#D0D0D0"/>
                        <TextBlock Text="• SM-S928BZYCMEA (Galaxy S24 Ultra Titanium Yellow)" 
                                   FontFamily="Consolas" FontSize="12" Foreground="#D0D0D0"/>
                        <TextBlock Text="• SM-S928BZKCMEA (Galaxy S24 Ultra Titanium Black)" 
                                   FontFamily="Consolas" FontSize="12" Foreground="#D0D0D0"/>
                    </StackPanel>
                </StackPanel>

                <!-- Information Section -->
                <StackPanel Spacing="6">
                    <TextBlock Text="Format: SM-[Model][Color][Region]" 
                               FontSize="12" 
                               Foreground="#A83232" 
                               FontWeight="Medium"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="Find official Samsung product codes on Samsung website."
                               FontSize="11" 
                               Foreground="#B8B8B8" 
                               TextWrapping="Wrap"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="⚠️ Always verify codes from official Samsung sources."
                               FontSize="11" 
                               Foreground="#FFD700" 
                               TextWrapping="Wrap"
                               HorizontalAlignment="Center"
                               FontWeight="Medium"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2"
                Background="#1E1E1E"
                BorderBrush="#3F3F3F"
                BorderThickness="0,1,0,0"
                Padding="14,10">
            <StackPanel Orientation="Horizontal"
                        Spacing="10"
                        HorizontalAlignment="Right">
                <Button x:Name="btnCancel"
                        Content="Cancel"
                        Click="btnCancel_Click"/>
                <Button x:Name="btnOK"
                        Content="Apply"
                        Classes="accent"
                        Click="btnOK_Click"
                        IsDefault="True"/>
            </StackPanel>
        </Border>
    </Grid>
</Window> 