d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\SamsungTool.exe
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\SamsungTool.deps.json
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\SamsungTool.runtimeconfig.json
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\SamsungTool.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\SamsungTool.pdb
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Base.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Controls.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.DesignerSupport.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Dialogs.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Markup.Xaml.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Markup.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Metal.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.MicroCom.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.OpenGL.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Vulkan.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Controls.ColorPicker.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Desktop.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Diagnostics.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Fonts.Inter.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.FreeDesktop.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Native.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Remote.Protocol.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Skia.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Themes.Fluent.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Themes.Simple.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Win32.Automation.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.Win32.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Avalonia.X11.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\CommunityToolkit.Mvvm.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\HarfBuzzSharp.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\MicroCom.Runtime.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Microsoft.Extensions.Logging.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Microsoft.Extensions.Options.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Microsoft.Extensions.Primitives.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Microsoft.Win32.SystemEvents.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\QRCoder.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\SkiaSharp.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\System.Diagnostics.EventLog.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\System.Drawing.Common.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\System.Private.Windows.Core.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\System.IO.Ports.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\System.ServiceProcess.ServiceController.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\Tmds.DBus.Protocol.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win-arm64\native\av_libglesv2.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win-x64\native\av_libglesv2.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win-x86\native\av_libglesv2.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\osx\native\libAvaloniaNative.dylib
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-arm\native\libHarfBuzzSharp.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-arm64\native\libHarfBuzzSharp.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-musl-x64\native\libHarfBuzzSharp.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-x64\native\libHarfBuzzSharp.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\osx\native\libHarfBuzzSharp.dylib
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win-arm64\native\libHarfBuzzSharp.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win-x64\native\libHarfBuzzSharp.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win-x86\native\libHarfBuzzSharp.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.Win32.SystemEvents.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\android-arm\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\android-arm64\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\android-x64\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\android-x86\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-bionic-arm64\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-bionic-x64\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-musl-arm\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-musl-arm64\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-musl-x64\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\maccatalyst-arm64\native\libSystem.IO.Ports.Native.dylib
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\maccatalyst-x64\native\libSystem.IO.Ports.Native.dylib
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-arm\native\libSkiaSharp.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-arm64\native\libSkiaSharp.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-musl-x64\native\libSkiaSharp.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\linux-x64\native\libSkiaSharp.so
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\osx\native\libSkiaSharp.dylib
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win-arm64\native\libSkiaSharp.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win-x64\native\libSkiaSharp.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win-x86\native\libSkiaSharp.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.Messages.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\unix\lib\net9.0\System.IO.Ports.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.IO.Ports.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.ServiceProcess.ServiceController.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\SamsungTool.csproj.AssemblyReference.cache
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\Avalonia\Resources.Inputs.cache
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\Avalonia\resources
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\SamsungTool.GeneratedMSBuildEditorConfig.editorconfig
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\SamsungTool.AssemblyInfoInputs.cache
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\SamsungTool.AssemblyInfo.cs
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\SamsungTool.csproj.CoreCompileInputs.cache
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\SamsungT.9D4B3B5B.Up2Date
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\SamsungTool.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\refint\SamsungTool.dll
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\SamsungTool.pdb
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\SamsungTool.genruntimeconfig.cache
d:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\obj\Debug\net9.0\ref\SamsungTool.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\K4os.Compression.LZ4.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\K4os.Compression.LZ4.Streams.dll
D:\Telegram_Download\SamsungTool_refactored_20250604173140 (3)2\bin\Debug\net9.0\K4os.Hash.xxHash.dll
