﻿using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using System;
using System.Threading.Tasks;

namespace SamsungTool
{
    public partial class InputSerial : Window
    {
        public string? Result { get; private set; }

        private TextBlock? _headerTitle;
        private TextBlock? _messageText;
        private TextBlock? _descriptionText;
        private TextBox? _inputBox;
        private Button? _okButton;
        private Button? _cancelButton;

        public InputSerial()
        {
            InitializeComponent();
            this.Opened += OnOpened;
        }

        public InputSerial(string title, string message, string? description = null)
        {
            InitializeComponent();

            if (!string.IsNullOrEmpty(title))
            {
                Title = title;
                if (_headerTitle != null)
                _headerTitle.Text = title;
            }

            if (!string.IsNullOrEmpty(message) && _messageText != null)
            {
                _messageText.Text = message;
            }

            if (!string.IsNullOrEmpty(description) && _descriptionText != null)
            {
                _descriptionText.Text = description;
                _descriptionText.IsVisible = true;
            }
            else if (_descriptionText != null)
            {
                _descriptionText.IsVisible = false;
            }

            if (_okButton != null)
            _okButton.Click += OkButton_Click;
            
            if (_cancelButton != null)
            _cancelButton.Click += CancelButton_Click;
            
            if (_inputBox != null)
            _inputBox.KeyDown += InputBox_KeyDown;

            this.Opened += OnOpened;
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);

            _headerTitle = this.FindControl<TextBlock>("HeaderTitle");
            _messageText = this.FindControl<TextBlock>("MessageText");
            _descriptionText = this.FindControl<TextBlock>("DescriptionText");
            _inputBox = this.FindControl<TextBox>("InputBox");
            _okButton = this.FindControl<Button>("OkButton");
            _cancelButton = this.FindControl<Button>("CancelButton");
        }

        private void OnOpened(object? sender, EventArgs e)
        {
            _inputBox?.Focus();
        }

        private void OkButton_Click(object? sender, RoutedEventArgs e)
        {
            Result = _inputBox?.Text;
            Close();
        }

        private void CancelButton_Click(object? sender, RoutedEventArgs e)
        {
            Result = null;
            Close();
        }

        private void InputBox_KeyDown(object? sender, Avalonia.Input.KeyEventArgs e)
        {
            if (e.Key == Avalonia.Input.Key.Enter)
            {
                Result = _inputBox?.Text;
                Close();
            }
            else if (e.Key == Avalonia.Input.Key.Escape)
            {
                Result = null;
                Close();
            }
        }

        public static async Task<string?> ShowAsync(Window parent, string title, string message, string? description = null)
        {
            var dialog = new InputSerial(title, message, description);
            await dialog.ShowDialog(parent);
            return dialog.Result;
        }
    }
}