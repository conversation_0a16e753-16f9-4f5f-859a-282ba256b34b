﻿"obj\Release\net9.0\win-x64\native\SamsungTool.obj"
/OUT:"bin\Release\net9.0\win-x64\native\SamsungTool.exe"
/DEF:"obj\Release\net9.0\win-x64\native\SamsungTool.def"
/LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64"
/LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\lib\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\\lib\10.0.26100.0\\um\x64"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.8\sdk\bootstrapper.obj"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.8\sdk\Runtime.WorkstationGC.lib"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.8\sdk\eventpipe-disabled.lib"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.8\sdk\Runtime.VxsortEnabled.lib"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.8\sdk\standalonegc-disabled.lib"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.8\sdk\zlibstatic.lib"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.8\sdk\System.Globalization.Native.Aot.lib"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.8\sdk\System.IO.Compression.Native.Aot.lib"
"advapi32.lib"
"bcrypt.lib"
"crypt32.lib"
"iphlpapi.lib"
"kernel32.lib"
"mswsock.lib"
"ncrypt.lib"
"normaliz.lib"
"ntdll.lib"
"ole32.lib"
"oleaut32.lib"
"secur32.lib"
"user32.lib"
"version.lib"
"ws2_32.lib"
/NOLOGO /MANIFEST:NO
/MERGE:.managedcode=.text /MERGE:hydrated=.bss
/INCREMENTAL:NO
/SUBSYSTEM:WINDOWS
/ENTRY:wmainCRTStartup /NOEXP /NOIMPLIB /STACK:1572864
/NATVIS:"C:\Users\<USER>\.nuget\packages\microsoft.dotnet.ilcompiler\9.0.8\build\NativeAOT.natvis"
/IGNORE:4104
/CETCOMPAT
/NODEFAULTLIB:libucrt.lib
/DEFAULTLIB:ucrt.lib
/OPT:REF
/OPT:ICF
