﻿using SamsungTool.Library;
using SamsungTool.Library.GUI;
using SamsungTool.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO.Ports;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations
{
    public class MtpService : IMtpService, IDisposable
    {
        private static readonly SemaphoreSlim _serialLock = new SemaphoreSlim(1, 1);
        private bool _disposed = false;

        public static SerialPort? Port;
        public static string? SNMTP { get; set; }

        public async Task ExecuteOperationAsync(string operation, int? os = null, CancellationToken cancellationToken = default)
        {
            await Task.Run(async () =>
            {
                cancellationToken.ThrowIfCancellationRequested();

                // Use the new device communication utility for port selection
                ServiceLoggingUtility.LogWaitingForDevice();
                string? Portname = await DeviceCommunicationUtility.GetSelectedPortNameAsync();

                cancellationToken.ThrowIfCancellationRequested();

                if (!DeviceCommunicationUtility.ValidatePortName(Portname))
                {
                    return;
                }

                // Use the new logging utility for device connection
                ServiceLoggingUtility.LogHighlight(Portname!);
                ServiceLoggingUtility.LogStep($"Connecting to {Portname}...");

                // Dispose existing port if any
                try
                {
                    if (Port != null)
                    {
                        if (Port.IsOpen)
                            Port.Close();
                        Port.Dispose();
                        Port = null;
                    }
                }
                catch (Exception)
                {
                    // Log but continue
                }

                // Add delay to ensure port is fully released
                await Task.Delay(500, cancellationToken);

                Port = new SerialPort
                {
                    PortName = Portname,
                    BaudRate = 115200,
                    ReadTimeout = 5000,
                    WriteTimeout = 5000
                };

                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    Port.Open();
                    if (Port.IsOpen)
                    {
                        RichLogs("Okay", Color.LimeGreen, true);
                        RichLogs("Reading device information [ MTP ]...", Color.Silver, true);
                        RichLogs("", Color.Silver, true);

                        int maxRetries = 3;
                        string? response = null;

                        for (int retry = 0; retry < maxRetries; retry++)
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            try
                            {
                                Port.DiscardInBuffer();
                                Port.DiscardOutBuffer();

                                if (retry == 0)
                                {
                                    await WriteToPortAsync(Port, "AT+SWATD=0\r\n", cancellationToken);
                                    await Task.Delay(1000, cancellationToken);
                                }

                                await WriteToPortAsync(Port, "AT+DEVCONINFO", cancellationToken);

                                response = await ReadFromPortWithTimeoutAsync(Port, 8000, cancellationToken);

                                if (!string.IsNullOrEmpty(response))
                                {
                                    if (response.Contains("+CME Error") ||
                                        response.Contains("PACM") ||
                                        response.Contains("UNREGISTED"))
                                    {
                                        response = null;
                                    }
                                    else if (response.Contains("#OK#"))
                                    {
                                        response = response.Replace("#OK#", "").Replace("OK", "").Trim();
                                        break;
                                    }
                                }

                                if (retry < maxRetries - 1)
                                {
                                    RichLogs($"Retry {retry + 1}/{maxRetries}...", Color.SkyBlue, true);
                                    await Task.Delay(2000, cancellationToken);
                                }
                            }
                            catch (OperationCanceledException)
                            {
                                throw;
                            }
                            catch (Exception ex)
                            {
                                if (retry < maxRetries - 1)
                                {
                                    RichLogs($"Error: {ex.Message}, retrying...", Color.SkyBlue, true);
                                    await Task.Delay(2000, cancellationToken);
                                }
                                else throw;
                            }
                        }

                        cancellationToken.ThrowIfCancellationRequested();

                        if (string.IsNullOrEmpty(response))
                        {
                            RichLogs("Failed to get valid device response after multiple attempts", Color.IndianRed, true);
                            return;
                        }

                        Console.WriteLine(response);
                        ProcessResponse(response);

                        if (operation == "rebootdownload")
                        {
                            RichLogs("", Color.Silver, true);
                            RichLogs("Rebooting to download mode...", Color.Silver, false);

                            await WriteToPortAsync(Port, "AT+FUS?\r\n", cancellationToken);
                            RichLogs("Okay", Color.LimeGreen, true);
                        }
                        
                        if (operation == "rebotnormal")
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            
                            RichLogs("", Color.Silver, true);
                            RichLogs("Rebooting to normal mode...", Color.Silver, true);
                            
                            // Step 1: AT+SWATD=0
                            RichLogs("Step 1: ", Color.Silver, false);
                            await WriteToPortAsync(Port, "AT+SWATD=0\r\n", cancellationToken);
                            
                            string swatdResponse = await ReadFromPortWithTimeoutAsync(Port, 3000, cancellationToken);
                            if (swatdResponse != null && (swatdResponse.Contains("[DR]: CHANGE TO DDEXE") || swatdResponse.Contains("CHANGE TO DDEXE")))
                            {
                                RichLogs("Okay", Color.LimeGreen, true);
                            }
                            else
                            {
                                RichLogs("Failed", Color.IndianRed, true);
                                return;
                            }
                            
                            // Step 2: AT+CFUN=1,1
                            RichLogs("Step 2: ", Color.Silver, false);
                            await WriteToPortAsync(Port, "AT+CFUN=1,1\r\n", cancellationToken);
                            
                            string cfunResponse = await ReadFromPortWithTimeoutAsync(Port, 5000, cancellationToken);
                            if (cfunResponse != null && cfunResponse.Contains("OK"))
                            {
                                RichLogs("Okay", Color.LimeGreen, true);
                                RichLogs("Device will reboot to normal mode", Color.Silver, true);
                            }
                            else
                            {
                                RichLogs("Failed", Color.IndianRed, true);
                                RichLogs("Device may not have responded correctly", Color.IndianRed, true);
                            }
                        }
                        if (operation == "factoryreset")
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            RichLogs("", Color.Silver, true);
                            RichLogs("Performing Factory Reset...", Color.Silver, false);

                            await WriteToPortAsync(Port, "AT+FACTORST=0,0\r\n", cancellationToken);

                            string factoryResetResponse = await ReadFromPortWithTimeoutAsync(Port, 5000, cancellationToken);

                            if (factoryResetResponse == "CANCELLED")
                            {
                                throw new OperationCanceledException();
                            }
                            else if (factoryResetResponse != null && factoryResetResponse.Contains("OK"))
                            {
                                RichLogs("Okay", Color.LimeGreen, true);
                                RichLogs("The device will restart automatically", Color.Silver, true);
                            }
                            else
                            {
                                RichLogs("Failed!", Color.IndianRed, true);
                                if (factoryResetResponse != null && factoryResetResponse.Contains("ERROR"))
                                    RichLogs("Device returned error", Color.IndianRed, true);
                                else
                                    RichLogs("No response from device", Color.IndianRed, true);
                            }
                        }
                        if (operation == "QR")
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            if (string.IsNullOrWhiteSpace(SNMTP))
                            {
                                RichLogs("Serial number empty – abort QR generation", Color.IndianRed, true);
                                return;
                            }

                            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(async () =>
                            {
                                var qrWin = new SamsungTool.QRWindow(os ?? 0, SNMTP);
                                if (MainWindow.GUI != null)
                                {
                                    await qrWin.ShowDialog(MainWindow.GUI);
                                }
                                else
                                {
                                    qrWin.Show();
                                }
                            });
                        }
                        
                        if (operation == "changecolor")
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            
                            // Show input dialog first to get new product code
                            string newProdCode = "";
                            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(async () =>
                            {
                                newProdCode = await InputProdcode.ShowAsync(MainWindow.GUI);
                            });
                            
                            if (string.IsNullOrWhiteSpace(newProdCode))
                            {
                                RichLogs("Operation cancelled by user", Color.Orange, true);
                                return;
                            }
                            
                            RichLogs("", Color.Silver, true);
                            RichLogs("Starting Color Code Change Process...", Color.Silver, true);
                            RichLogs($"Target Product Code: {newProdCode}", Color.SkyBlue, true);
                            
                            // Step 1: AT+SWATD=0
                            RichLogs("Step 1: ", Color.Silver, false);
                            await WriteToPortAsync(Port, "AT+SWATD=0\r\n", cancellationToken);
                            
                            string swatdResponse = await ReadFromPortWithTimeoutAsync(Port, 3000, cancellationToken);
                            if (swatdResponse != null && (swatdResponse.Contains("[DR]: CHANGE TO DDEXE") || swatdResponse.Contains("CHANGE TO DDEXE")))
                            {
                                RichLogs("Okay", Color.LimeGreen, true);
                            }
                            else
                            {
                                RichLogs("Failed", Color.IndianRed, true);
                                return;
                            }
                            
                            // Step 2: AT+ACTIVATE=0,0,0 with 15s wait and 2s interval checks
                            RichLogs("Step 2: ", Color.Silver, false);
                            await WriteToPortAsync(Port, "AT+ACTIVATE=0,0,0\r\n", cancellationToken);
                            
                            bool activateSuccess = false;
                            int attempts = 0;
                            int maxAttempts = 8; // 15 seconds / 2 seconds = 7.5, so 8 attempts
                            
                            while (attempts < maxAttempts && !activateSuccess)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                
                                await Task.Delay(2000, cancellationToken); // Wait 2 seconds
                                attempts++;
                                
                                string activateResponse = await ReadFromPortWithTimeoutAsync(Port, 1000, cancellationToken);
                                
                                if (activateResponse != null && activateResponse.Contains("+ACTIVATE:0,OK") && 
                                    activateResponse.Contains("OK"))
                                {
                                    activateSuccess = true;
                                    RichLogs("Okay", Color.LimeGreen, true);
                                }
                                else if (!string.IsNullOrEmpty(activateResponse))
                                {
                                    // Continue waiting if we got some response but not the expected one
                                    RichLogs(".", Color.SkyBlue, false);
                                }
                            }
                            
                            if (!activateSuccess)
                            {
                                RichLogs("Failed", Color.IndianRed, true);
                                return;
                            }
                            
                            // Step 3: AT+SWATD=1
                            RichLogs("Step 3: ", Color.Silver, false);
                            await WriteToPortAsync(Port, "AT+SWATD=1\r\n", cancellationToken);
                            
                            string swatd1Response = await ReadFromPortWithTimeoutAsync(Port, 3000, cancellationToken);
                            if (swatd1Response != null && (swatd1Response.Contains("[DR]: CHANGE TO ATD") || swatd1Response.Contains("CHANGE TO ATD")))
                            {
                                RichLogs("Okay", Color.LimeGreen, true);
                            }
                            else
                            {
                                RichLogs("Failed", Color.IndianRed, true);
                                return;
                            }
                            
                            // Step 4: AT+PRODCODE=1,0 (get current product code)
                            RichLogs("Step 4: ", Color.Silver, false);
                            await WriteToPortAsync(Port, "AT+PRODCODE=1,0\r\n", cancellationToken);
                            
                            string prodcodeReadResponse = await ReadFromPortWithTimeoutAsync(Port, 5000, cancellationToken);
                            string currentProdCode = "";
                            
                            if (prodcodeReadResponse != null && prodcodeReadResponse.Contains("+PRODCODE:1,"))
                            {
                                // Extract product code from response
                                var match = System.Text.RegularExpressions.Regex.Match(prodcodeReadResponse, @"\+PRODCODE:1,(.+?)(?:\r|\n|$)");
                                if (match.Success)
                                {
                                    currentProdCode = match.Groups[1].Value.Trim();
                                    RichLogs("Okay", Color.LimeGreen, true);
                                    RichLogs("Current Product Code: ", Color.Silver, false);
                                    RichLogs(currentProdCode, Color.SkyBlue, true);
                                }
                                else
                                {
                                    RichLogs("Failed", Color.IndianRed, true);
                                    return;
                                }
                            }
                            else
                            {
                                RichLogs("Failed", Color.IndianRed, true);
                                return;
                            }
                            
                            RichLogs("Step 5: Applying new product code ", Color.Silver, false);
                            RichLogs(newProdCode, Color.SkyBlue, false);
                            RichLogs("...", Color.Silver, false);
                            await WriteToPortAsync(Port, $"AT+PRODCODE=2;{newProdCode}\r\n", cancellationToken);
                            
                            string prodcodeSetResponse = await ReadFromPortWithTimeoutAsync(Port, 5000, cancellationToken);
                            
                            if (prodcodeSetResponse != null && prodcodeSetResponse.Contains("+PRODCODE:2,OK"))
                            {
                                RichLogs("Okay", Color.LimeGreen, true);
                                RichLogs("Product code changed successfully!", Color.LimeGreen, true);
                                RichLogs("", Color.Silver, true);
                                RichLogs("Important: ", Color.Orange, false);
                                RichLogs("Please factory reset and activate your phone", Color.Orange, true);
                                RichLogs("to ensure the new color code is properly recognized.", Color.Orange, true);
                            }
                        }
                    }
                    else
                    {
                        RichLogs("Failed to open port", Color.IndianRed, true);
                    }
                }
                catch (OperationCanceledException)
                {
                    RichLogs("Operation was cancelled", Color.Orange, true);
                    throw;
                }
                catch (Exception ex)
                {
                    RichLogs($"{ex.Message}", Color.IndianRed, true);
                }
                finally
                {
                    // Improved cleanup with retry logic
                    try
                    {
                        if (Port?.IsOpen == true)
                        {
                            Port.Close();
                        }
                    }
                    catch (Exception)
                    {
                        // Ignore close errors
                    }

                    try
                    {
                        Port?.Dispose();
                        Port = null;
                    }
                    catch (Exception)
                    {
                        // Ignore disposal errors
                    }

                    // Small delay to ensure port is fully released
                    await Task.Delay(200, CancellationToken.None);

                    RichLogs("", Color.Silver, true);
                    RichLogs("Operation completed", Color.LimeGreen, true);
                }
            }, cancellationToken);
        }

        public void ProcessResponse(string response)
        {
            if (response.Contains("+DEVCONINFO:"))
            {
                response = response.Substring(
                    response.IndexOf("+DEVCONINFO:") + "+DEVCONINFO:".Length
                ).Trim();
            }

            var responseValues = new Dictionary<string, string>();
            foreach (var part in response.Split(';'))
            {
                if (string.IsNullOrWhiteSpace(part))
                    continue;

                var kv = part.Split('(', 2);
                if (kv.Length != 2)
                    continue;

                var key = kv[0].Trim();
                var value = kv[1].TrimEnd(')');
                responseValues[key] = value;
                if (key == "SN")
                    SNMTP = value;
            }

            const int labelWidth = 15;
            void LogLine(string lbl, string val, string? date = null)
            {
                var padded = lbl.PadRight(labelWidth);
                RichLogs(padded + ": ", System.Drawing.Color.Silver, false);
                var text = val + (date != null ? $" [{date}]" : string.Empty);
                RichLogs(text, System.Drawing.Color.CornflowerBlue, true);
            }

            if (responseValues.TryGetValue("MN", out var model))
                LogLine("Model", model);
            if (responseValues.TryGetValue("BASE", out var baseVal))
                LogLine("Base", baseVal);

            var apVersion = string.Empty;
            var blVersion = string.Empty;
            var cpVersion = string.Empty;
            var cscVersion = string.Empty;
            if (responseValues.TryGetValue("VER", out var verValue))
            {
                var vs = verValue.Split('/');
                if (vs.Length == 4)
                {
                    apVersion = vs[0];
                    blVersion = vs[1];
                    cpVersion = vs[2];
                    cscVersion = vs[3];
                }
                else
                {
                    apVersion = blVersion = cpVersion = cscVersion = verValue;
                }
            }

            if (!string.IsNullOrEmpty(apVersion))
                LogLine("AP version", apVersion, GetFirmwareDate(apVersion));
            if (!string.IsNullOrEmpty(blVersion))
                LogLine("BL version", blVersion, GetFirmwareDate(blVersion));
            if (!string.IsNullOrEmpty(cpVersion))
                LogLine("CP version", cpVersion, GetFirmwareDate(cpVersion));
            if (!string.IsNullOrEmpty(cscVersion))
                LogLine("CSC version", cscVersion, GetFirmwareDate(cscVersion));

            var labels = new Dictionary<string, string>
    {
        {"MNC",     "MNC"},
        {"MCC",     "MCC"},
        {"PRD",     "CSC"},
        {"AID",     "AID"},
        {"CC",      "Country"},
        {"OMCCODE", "OMC Code"},
        {"SN",      "SN"},
        {"IMEI",    "IMEI"},
        {"UN",      "Unique number"},
        {"PN",      "Phone number"},
        {"CON",     "USB mode"},
        {"LOCK",    "Lock status"},
        {"LIMIT",   "Limit"},
        {"SDP",     "SDP"},
        {"HVID",    "HVID"}
    };

            foreach (var key in labels.Keys)
            {
                if (responseValues.TryGetValue(key, out var val))
                    LogLine(labels[key], val);
            }
        }

        public string GetFirmwareDate(string version)
        {
            if (string.IsNullOrEmpty(version) || version.Length < 3)
                return string.Empty;

            string year = GetYearFromVersion(version);
            string month = GetMonthFromVersion(version);
            string revision = GetRevFromVersion(version);

            if (year == "Unknown" || month == "Unknown")
                return string.Empty;

            return $"{month} {year}, {revision}";
        }

        private static string GetYearFromVersion(string version)
        {
            if (version.Length >= 3)
            {
                char yearChar = version[version.Length - 3];
                switch (yearChar)
                {
                    case 'S': return "2019";
                    case 'T': return "2020";
                    case 'U': return "2021";
                    case 'V': return "2022";
                    case 'W': return "2023";
                    case 'X': return "2024";
                    case 'Y': return "2025";
                    case 'Z': return "2026";
                    default: return "Unknown";
                }
            }
            return "Unknown";
        }

        private static string GetMonthFromVersion(string version)
        {
            if (version.Length >= 2)
            {
                char monthChar = version[version.Length - 2];
                switch (monthChar)
                {
                    case 'A': return "January";
                    case 'B': return "February";
                    case 'C': return "March";
                    case 'D': return "April";
                    case 'E': return "May";
                    case 'F': return "June";
                    case 'G': return "July";
                    case 'H': return "August";
                    case 'I': return "September";
                    case 'J': return "October";
                    case 'K': return "November";
                    case 'L': return "December";
                    default: return "Unknown";
                }
            }
            return "Unknown";
        }

        private static string GetRevFromVersion(string version)
        {
            if (version.Length >= 1)
            {
                char revChar = version[version.Length - 1];
                return "Rev" + revChar;
            }
            return "Unknown";
        }

        private async Task WriteToPortAsync(SerialPort port, string data, CancellationToken cancellationToken)
        {
            await _serialLock.WaitAsync(cancellationToken);
            try
            {
                byte[] buffer = Encoding.ASCII.GetBytes(data);
                await port.BaseStream.WriteAsync(buffer, 0, buffer.Length, cancellationToken);
                await port.BaseStream.FlushAsync(cancellationToken);
            }
            finally
            {
                _serialLock.Release();
            }
        }

        private async Task<string> ReadFromPortWithTimeoutAsync(SerialPort port, int timeoutMs, CancellationToken cancellationToken)
        {
            using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(timeoutMs);

            var buffer = new byte[4096];
            var result = new StringBuilder();

            try
            {
                while (!cts.Token.IsCancellationRequested)
                {
                    if (port.BytesToRead > 0)
                    {
                        int bytesToRead = Math.Min(buffer.Length, port.BytesToRead);
                        int bytesRead = await port.BaseStream.ReadAsync(buffer, 0, bytesToRead, cts.Token);

                        if (bytesRead > 0)
                        {
                            string data = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                            result.Append(data);

                            if (result.ToString().Contains("#OK#") ||
                                result.ToString().Contains("ERROR") ||
                                result.ToString().Contains("OK"))
                            {
                                break;
                            }
                        }
                    }
                    else
                    {
                        await Task.Delay(50, cts.Token);
                    }
                }

                return result.ToString();
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                return "CANCELLED";
            }
            catch (OperationCanceledException)
            {
                return result.ToString();
            }
            catch (Exception ex)
            {
                return "ERROR: " + ex.Message;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                try
                {
                    // Close and dispose the static port
                    if (Port?.IsOpen == true)
                    {
                        Port.Close();
                    }
                }
                catch (Exception)
                {
                    // Log error but continue disposal
                }

                try
                {
                    Port?.Dispose();
                    Port = null;
                }
                catch (Exception)
                {
                    // Log error but continue disposal
                }

                try
                {
                    _serialLock?.Dispose();
                }
                catch (Exception)
                {
                    // Log error but continue disposal
                }
            }

            _disposed = true;
        }

        /// <summary>
        /// Static method to cleanup the static Port if needed
        /// </summary>
        public static void CleanupStaticPort()
        {
            try
            {
                if (Port?.IsOpen == true)
                {
                    Port.Close();
                }
                Port?.Dispose();
                Port = null;
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        ~MtpService()
        {
            Dispose(false);
        }
    }
}