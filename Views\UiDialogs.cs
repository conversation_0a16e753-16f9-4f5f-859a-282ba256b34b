using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Layout;
using Avalonia.Media;
using Avalonia.Threading;

namespace SamsungTool.Views
{
    public static class UiDialogs
    {
        public static async Task ShowOkAsync(Window owner, string title, string message, bool topmost = false, bool undecoratedAndLocked = false)
        {
            await Dispatcher.UIThread.InvokeAsync(async () =>
            {
                var dlg = new Window
                {
                    Title = title,
                    Width = 360,
                    Height = 180,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Background = new SolidColorBrush(Color.Parse("#1A1A1A")),
                    CanResize = false,
                    ShowInTaskbar = false,
                    Topmost = topmost,
                    SystemDecorations = undecoratedAndLocked ? SystemDecorations.None : SystemDecorations.BorderOnly
                };

                var main = new Grid();

                if (undecoratedAndLocked)
                {
                    var bar = new Border
                    {
                        Height = 34,
                        Background = new SolidColorBrush(Color.Parse("#141414")),
                        BorderBrush = new SolidColorBrush(Color.Parse("#2E2E2E")),
                        BorderThickness = new Thickness(0,0,0,1),
                        Child = new TextBlock
                        {
                            Text = title,
                            Foreground = new SolidColorBrush(Color.Parse("#E0E0E0")),
                            FontSize = 13,
                            FontWeight = FontWeight.Medium,
                            VerticalAlignment = VerticalAlignment.Center,
                            Margin = new Thickness(10, 0, 0, 0)
                        }
                    };
                    main.RowDefinitions.Add(new RowDefinition(GridLength.Auto));
                    main.RowDefinitions.Add(new RowDefinition(1, GridUnitType.Star));
                    Grid.SetRow(bar, 0);
                    main.Children.Add(bar);
                }

                var content = new StackPanel
                {
                    Margin = new Thickness(20),
                    VerticalAlignment = VerticalAlignment.Center,
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    Spacing = 20
                };

                var txt = new TextBlock
                {
                    Text = message,
                    TextWrapping = TextWrapping.Wrap,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FontSize = 14,
                    Foreground = new SolidColorBrush(Color.Parse("#F2F2F2"))
                };

                var ok = new Button
                {
                    Content = "OK",
                    Width = 110,
                    Height = 34,
                    FontSize = 13,
                    FontWeight = FontWeight.Medium,
                    Background = new SolidColorBrush(Color.Parse("#B83838")),
                    Foreground = new SolidColorBrush(Colors.White),
                    BorderThickness = new Thickness(0),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    HorizontalContentAlignment = HorizontalAlignment.Center,
                    VerticalContentAlignment = VerticalAlignment.Center,
                    IsDefault = true
                };

                bool allowClose = !undecoratedAndLocked;
                ok.Click += (s, e) => { allowClose = true; dlg.Close(); };
                if (undecoratedAndLocked)
                {
                    dlg.Closing += (s, e) => { if (!allowClose) e.Cancel = true; };
                }

                content.Children.Add(txt);
                var okHost = new Grid
                {
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    ColumnDefinitions = new ColumnDefinitions("*,Auto,*")
                };
                Grid.SetColumn(ok, 1);
                okHost.Children.Add(ok);
                content.Children.Add(okHost);

                if (undecoratedAndLocked)
                {
                    Grid.SetRow(content, 1);
                }

                main.Children.Add(content);
                dlg.Content = main;
                await dlg.ShowDialog(owner);
            });
        }
    }
}


