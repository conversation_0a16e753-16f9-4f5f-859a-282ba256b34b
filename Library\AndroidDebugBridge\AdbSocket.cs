#define TRACE

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net.Sockets;
using System.Text;

namespace SamsungTool.Library.AndroidDebugBridge;

public class AdbSocket : IDisposable
{
    private TcpClient? _tcpClient;

    private NetworkStream? _tcpStream;

    private Encoding _encoding = Encoding.ASCII;

    private byte[] _buffer = new byte[65536];

    public AdbSocket(string adbServerHost, int adbServerPort)
    {
        _tcpClient = new TcpClient(adbServerHost, adbServerPort);
        _tcpStream = _tcpClient.GetStream();
    }

    public void Dispose()
    {
        if (_tcpClient != null)
        {
            _tcpClient.Close();
            _tcpClient = null;
        }
        if (_tcpStream != null)
        {
            _tcpStream.Close();
            _tcpStream = null;
        }
    }

    public void Write(byte[] data, int size)
    {
        _tcpStream!.Write(data, 0, size);
    }

    public void Write(byte[] data)
    {
        Write(data, data.Length);
    }

    public void WriteString(string text)
    {
        int bytes = _encoding.GetBytes(text, 0, text.Length, _buffer, 0);
        Write(_buffer, bytes);
    }

    public void WriteInt32(int number)
    {
        byte[] bytes = BitConverter.GetBytes(number);
        Write(bytes);
    }

    public void SendCommand(string command)
    {
        WriteString($"{command.Length:X04}");
        WriteString(command);
        string text = ReadString(4);
        string text2 = text;
        string text3 = text2;
        if (!(text3 == "OKAY"))
        {
            if (!(text3 == "FAIL"))
            {
                throw new AdbInvalidResponseException(text);
            }
            string message = ReadHexString();
            throw new AdbException(message);
        }
    }

    public string? SendSyncCommand(string command, string parameter, bool readResponse = true)
    {
        WriteString(command);
        WriteInt32(parameter.Length);
        WriteString(parameter);
        if (!readResponse)
        {
            return null;
        }
        string text = ReadString(4);
        if (!text.Equals("FAIL"))
        {
            return text;
        }
        string message = ReadSyncString();
        throw new AdbException(message);
    }

    public void Read(byte[] data, int size)
    {
        int num;
        for (int i = 0; i < size; i += num)
        {
            num = _tcpStream!.Read(data, i, size - i);
        }
    }

    public byte[] Read(int size)
    {
        byte[] array = new byte[size];
        Read(array, size);
        return array;
    }

    public string ReadHexString()
    {
        int length = ReadInt32Hex();
        return ReadString(length);
    }

    public string ReadSyncString()
    {
        int length = ReadInt32();
        return ReadString(length);
    }

    public string ReadString(int length)
    {
        Read(_buffer, length);
        return _encoding.GetString(_buffer, 0, length);
    }

    public int ReadInt32()
    {
        Read(_buffer, 4);
        return BitConverter.ToInt32(_buffer, 0);
    }

    public int ReadInt32Hex()
    {
        Read(_buffer, 4);
        string @string = _encoding.GetString(_buffer, 0, 4);
        return Convert.ToInt32(@string, 16);
    }

    public string[] ReadAllLines()
    {
        List<string> list = new List<string>();
        using (StreamReader streamReader = new StreamReader(_tcpStream!, _encoding))
        {
            while (true)
            {
                string? text = streamReader.ReadLine();
                if (text != null)
                {
                    list.Add(text.Trim());
                    continue;
                }
                break;
            }
        }
        return list.ToArray();
    }
}