<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="SamsungTool.QRWindow"
        Title="Samsung QR Code"
        Width="550"
        Height="650"
        WindowStartupLocation="CenterScreen"
        Background="#1E1E1E"
        CanResize="False">

	<Window.Styles>
		<Style Selector="TextBlock.title">
			<Setter Property="HorizontalAlignment" Value="Center"/>
			<Setter Property="Foreground" Value="#58A6FF"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="FontSize" Value="20"/>
			<Setter Property="Margin" Value="0,0,0,8"/>
		</Style>

		<Style Selector="TextBlock.subtitle">
			<Setter Property="HorizontalAlignment" Value="Center"/>
			<Setter Property="Foreground" Value="#999999"/>
			<Setter Property="FontWeight" Value="Normal"/>
			<Setter Property="FontSize" Value="14"/>
			<Setter Property="Margin" Value="0,0,0,10"/>
		</Style>

		<Style Selector="Border.qr-border">
			<Setter Property="Background" Value="#23272E"/>
			<Setter Property="BorderBrush" Value="#33363B"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="8"/>
			<Setter Property="Padding" Value="20"/>
			<Setter Property="HorizontalAlignment" Value="Center"/>
			<Setter Property="Width" Value="450"/>
			<Setter Property="Height" Value="450"/>
			<Setter Property="Margin" Value="0,0,0,25"/>
			<Setter Property="BoxShadow" Value="0 4 12 0 #30000000"/>
		</Style>

		<Style Selector="Button.close-button">
			<Setter Property="Background" Value="#A83232"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderBrush" Value="#A83232"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="6"/>
			<Setter Property="FontWeight" Value="Medium"/>
			<Setter Property="FontSize" Value="15"/>
			<Setter Property="Width" Value="120"/>
			<Setter Property="Height" Value="42"/>
			<Setter Property="HorizontalAlignment" Value="Center"/>
			<Setter Property="HorizontalContentAlignment" Value="Center"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="Padding" Value="0"/>
		</Style>

		<Style Selector="Button.close-button:pointerover /template/ ContentPresenter">
			<Setter Property="Background" Value="#B83838"/>
			<Setter Property="BorderBrush" Value="#B83838"/>
		</Style>

		<Style Selector="Button.close-button:pressed /template/ ContentPresenter">
			<Setter Property="Background" Value="#A83232"/>
			<Setter Property="BorderBrush" Value="#A83232"/>
		</Style>
	</Window.Styles>

	<StackPanel Margin="40,35,40,30">
		<TextBlock Text="Samsung Device QR Code" Classes="title"/>
		<TextBlock Text="Scan the QR code below:" Classes="subtitle"/>

		<Border Classes="qr-border">
			<Image x:Name="QrImage"
                   Stretch="Uniform"
                   Width="400"
                   Height="400"/>
		</Border>

		<Button Content="Close"
                Classes="close-button"
                Click="CloseButton_Click"/>
	</StackPanel>
</Window>