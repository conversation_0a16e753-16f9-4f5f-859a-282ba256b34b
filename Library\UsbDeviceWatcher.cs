using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Avalonia.Threading;
using System.IO.Ports;
using System.Diagnostics;
using System.Text.RegularExpressions;
using System.Linq;
using SamsungTool.Library;

namespace SamsungTool.Library
{
    public class UsbDeviceWatcher : IDisposable
    {
        private const int WM_DEVICECHANGE = 0x0219;
        private const int DBT_DEVICEARRIVAL = 0x8000;
        private const int DBT_DEVICEREMOVECOMPLETE = 0x8004;
        private const int DBT_DEVTYP_PORT = 0x00000003;
        private const int DEVICE_NOTIFY_WINDOW_HANDLE = 0x00000000;

        [StructLayout(LayoutKind.Sequential)]
        private struct DEV_BROADCAST_HDR
        {
            public int dbch_size;
            public int dbch_devicetype;
            public int dbch_reserved;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        private struct DEV_BROADCAST_PORT
        {
            public int dbcp_size;
            public int dbcp_devicetype;
            public int dbcp_reserved;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 255)]
            public string dbcp_name;
        }

        [DllImport("user32.dll", SetLastError = true)]
        private static extern IntPtr RegisterDeviceNotification(IntPtr hRecipient, IntPtr NotificationFilter, int Flags);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool UnregisterDeviceNotification(IntPtr Handle);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateEvent(IntPtr lpEventAttributes, bool bManualReset, bool bInitialState, string lpName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetEvent(IntPtr hEvent);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool CloseHandle(IntPtr hObject);

        private IntPtr _notificationHandle = IntPtr.Zero;
        private bool _disposed = false;
        private static UsbDeviceWatcher? _instance;
        private static readonly object _lock = new object();

        public static event Action? UsbDeviceChanged;

        public static UsbDeviceWatcher Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new UsbDeviceWatcher();
                        }
                    }
                }
                return _instance;
            }
        }

        private UsbDeviceWatcher()
        {
            try
            {
                StartWatching();
            }
            catch (Exception ex)
            {
                // Log error for debugging but continue silently for production
                ErrorLogger.LogError("UsbDeviceWatcher.Constructor", ex);
            }
        }

        private void StartWatching()
        {
            try
            {
                // Create a Samsung-specific device watcher
                Task.Run(async () =>
                {
                    while (!_disposed)
                    {
                        try
                        {
                            // Check for Samsung device changes with smart detection
                            await CheckForSamsungDeviceChanges();
                            await Task.Delay(5000); // Reduced frequency: Check every 5 seconds instead of 2
                        }
                        catch (Exception ex)
                        {
                            // Log error for debugging but continue silently
                            ErrorLogger.LogError("UsbDeviceWatcher.StartWatching", ex);
                            await Task.Delay(10000); // Wait longer on error: 10 seconds instead of 5
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                // Log error for debugging but continue silently for production
                ErrorLogger.LogError("UsbDeviceWatcher.StartWatching.Main", ex);
            }
        }

        private static string _lastSamsungDevicesSignature = "";
        
        private async Task CheckForSamsungDeviceChanges()
        {
            try
            {
                // Get current Samsung devices specifically
                var currentSamsungDevices = await GetCurrentSamsungDevicesAsync();
                var currentSignature = string.Join("|", currentSamsungDevices.OrderBy(d => d));
                
                // Only trigger event if Samsung devices actually changed
                if (currentSignature != _lastSamsungDevicesSignature)
                {
                    _lastSamsungDevicesSignature = currentSignature;

                    // Reduced delay from 300ms to 200ms for faster response
                    await Task.Delay(200);

                    // Trigger event on UI thread
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        UsbDeviceChanged?.Invoke();
                    });
                }
            }
            catch (Exception ex)
            {
                // Log error for debugging but continue silently for production
                ErrorLogger.LogError("UsbDeviceWatcher.CheckForSamsungDeviceChanges", ex);
            }
        }

        private async Task<string[]> GetCurrentSamsungDevicesAsync()
        {
            var samsungDevices = new System.Collections.Generic.List<string>();
            
            try
            {
                // Optimized PowerShell query with faster execution
                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = "-NoProfile -ExecutionPolicy Bypass -Command \"Get-WmiObject -Class Win32_SerialPort -Filter \\\"Name LIKE '%SAMSUNG%'\\\" | Select-Object Name, DeviceID | ForEach-Object { $_.Name + '|' + $_.DeviceID }\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using (var process = new Process { StartInfo = startInfo })
                {
                    process.Start();

                    var output = await process.StandardOutput.ReadToEndAsync();
                    // Reduced timeout from 3000ms to 2000ms for faster response
                    await Task.Run(() => process.WaitForExit(2000));

                    if (process.ExitCode == 0 && !string.IsNullOrWhiteSpace(output))
                    {
                        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                        
                        foreach (var line in lines)
                        {
                            var cleanLine = line.Trim();
                            if (!string.IsNullOrWhiteSpace(cleanLine) && 
                                cleanLine.Contains("SAMSUNG", StringComparison.OrdinalIgnoreCase))
                            {
                                samsungDevices.Add(cleanLine);
                            }
                        }
                    }
                }

                // Fallback: Check PnP devices if serial port query failed
                if (samsungDevices.Count == 0)
                {
                    samsungDevices.AddRange(await GetSamsungPnPDevicesAsync());
                }
            }
            catch
            {
                // Silent error handling for production
            }

            return samsungDevices.ToArray();
        }

        private async Task<string[]> GetSamsungPnPDevicesAsync()
        {
            var samsungDevices = new System.Collections.Generic.List<string>();
            
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = "-NoProfile -ExecutionPolicy Bypass -Command \"Get-WmiObject -Class Win32_PnPEntity -Filter \\\"Name LIKE '%SAMSUNG%' AND Name LIKE '%COM%'\\\" | Select-Object Name, DeviceID | ForEach-Object { $_.Name + '|' + $_.DeviceID }\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using (var process = new Process { StartInfo = startInfo })
                {
                    process.Start();

                    var output = await process.StandardOutput.ReadToEndAsync();
                    // Reduced timeout from 3000ms to 2000ms for faster response
                    await Task.Run(() => process.WaitForExit(2000));

                    if (process.ExitCode == 0 && !string.IsNullOrWhiteSpace(output))
                    {
                        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                        
                        foreach (var line in lines)
                        {
                            var cleanLine = line.Trim();
                            if (!string.IsNullOrWhiteSpace(cleanLine) && 
                                cleanLine.Contains("SAMSUNG", StringComparison.OrdinalIgnoreCase) &&
                                cleanLine.Contains("COM", StringComparison.OrdinalIgnoreCase))
                            {
                                // Verify the COM port actually exists
                                var comMatch = Regex.Match(cleanLine, @"COM(\d+)", RegexOptions.IgnoreCase);
                                if (comMatch.Success)
                                {
                                    var comPort = comMatch.Value;
                                    var availablePorts = SerialPort.GetPortNames();
                                    if (availablePorts.Contains(comPort, StringComparer.OrdinalIgnoreCase))
                                    {
                                        samsungDevices.Add(cleanLine);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch
            {
                // Silent error handling for production
            }

            return samsungDevices.ToArray();
        }

        public static void Initialize()
        {
            // Just access the Instance to ensure it's created
            _ = Instance;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                try
                {
                    if (_notificationHandle != IntPtr.Zero)
                    {
                        UnregisterDeviceNotification(_notificationHandle);
                        _notificationHandle = IntPtr.Zero;
                    }
                }
                catch
                {
                    // Silent error handling for production
                }
            }

            _disposed = true;
        }

        ~UsbDeviceWatcher()
        {
            Dispose(false);
        }
    }
} 