﻿using System;

namespace SamsungTool.Library
{
    public static class Misc
    {
        public static string ExtractSubstring(string source, string startMarker, string endMarker)
        {
            if (string.IsNullOrEmpty(source))
                return string.Empty;

            ReadOnlySpan<char> sourceSpan = source.AsSpan();

            int startIndex = 0;
            if (!string.IsNullOrEmpty(startMarker))
            {
                ReadOnlySpan<char> startSpan = startMarker.AsSpan();
                int foundIndex = sourceSpan.IndexOf(startSpan);
                if (foundIndex < 0)
                    return string.Empty;

                startIndex = foundIndex + startSpan.Length;
            }

            int endIndex = sourceSpan.Length;
            if (!string.IsNullOrEmpty(endMarker))
            {
                ReadOnlySpan<char> endSpan = endMarker.AsSpan();
                ReadOnlySpan<char> searchSpan = sourceSpan.Slice(startIndex);
                int foundIndex = searchSpan.IndexOf(endSpan);
                if (foundIndex >= 0)
                    endIndex = startIndex + foundIndex;
            }

            if (startIndex >= endIndex)
                return string.Empty;

            return sourceSpan.Slice(startIndex, endIndex - startIndex).ToString();
        }
    }
}