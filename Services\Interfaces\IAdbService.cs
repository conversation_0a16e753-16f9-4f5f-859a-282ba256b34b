﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Services.Interfaces
{
    public interface IAdbService
    {
        Task<bool> InitializeAsync(CancellationToken cancellationToken = default);
        Task<bool> CheckServerAsync();
        Task<bool> FindDeviceAsync(CancellationToken cancellationToken = default);
        Task<bool> ReadDeviceInfoAsync(CancellationToken cancellationToken = default);
        Task ExecuteOperationAsync(string operation, CancellationToken cancellationToken = default);
        Task KgRemove2025Async(CancellationToken cancellationToken = default);
        Task ExecuteServerBinaryOperationAsync(string operationName, string jobName, bool requiresInitialSetup = true, CancellationToken cancellationToken = default);
        Task ExecuteRawShellCommandsAsync(string commandsText, CancellationToken cancellationToken = default);
        Task DisableServiceAsync(CancellationToken cancellationToken = default);
        string GetSerialNumber();
    }
}